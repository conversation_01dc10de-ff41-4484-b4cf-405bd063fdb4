<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.thinkgem.jeesite</groupId>
	<artifactId>xmCertificate</artifactId>
	<version>0.0.1</version>
	<packaging>war</packaging>
	
	<name>xmCertificate</name>
	<url>http://thinkgem.github.com/jeesite</url>
	<inceptionYear>2018-</inceptionYear>

	<!-- 项目属性 -->
	<properties>

		<!-- main version setting -->
		<spring.version>5.2.6.RELEASE</spring.version>
		<validator.version>5.2.4.Final</validator.version>
		<mybatis.version>3.2.8</mybatis.version>
		<mybatis-spring.version>1.2.3</mybatis-spring.version>
		<druid.version>1.1.22</druid.version>
		<ehcache.version>2.6.11</ehcache.version>
		<ehcache-web.version>2.0.4</ehcache-web.version>
		<shiro.version>1.9.1</shiro.version>
		<sitemesh.version>2.4.2</sitemesh.version>

		<!-- tools version setting -->
		<slf4j.version>1.7.30</slf4j.version>
		<commons-lang3.version>3.3.2</commons-lang3.version>
		<commons-io.version>2.4</commons-io.version>
		<commons-codec.version>1.9</commons-codec.version>
		<commons-fileupload.version>1.4</commons-fileupload.version>
		<commons-beanutils.version>1.9.4</commons-beanutils.version>
		<commons-email.version>1.5</commons-email.version>
		<jackson.version>2.11.0</jackson.version>
		<fastjson.version>1.2.83</fastjson.version>
		<xstream.version>1.4.11.1</xstream.version>
		<guava.version>28.2-jre</guava.version>
		<dozer.version>5.5.1</dozer.version>
		<poi.version>4.1.2</poi.version>
		<freemarker.version>2.3.20</freemarker.version>

		<!-- JWT -->
		<version.javaee_api>7.0</version.javaee_api>
		<version.shiro>1.9.1</version.shiro>
		<version.nimbus-jose-jwt>8.14.1</version.nimbus-jose-jwt>

		<!-- jdbc driver setting -->
		<mysql.driver.version>8.0.20</mysql.driver.version>
		<oracle.driver.version>10.2.0.4.0</oracle.driver.version>
		<mssql.driver.version>1.3.1</mssql.driver.version>

		<!-- environment setting -->
		<jdk.version>1.8</jdk.version>
		<tomcat.version>2.2</tomcat.version>
		<jetty.version>7.6.14.v20131031</jetty.version>
		<webserver.port>81</webserver.port>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<downloadSources>true</downloadSources>



	</properties>

	<!-- 设定主仓库，按设定顺序进行查找。 -->
	<repositories>

		<repository>
			<id>jeesite-repos</id>
			<name>Jeesite Repository</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public</url>
		</repository>
		<repository>
			<id>atlassian</id>
			<name>atlassian</name>
			<url>http://repository.atlassian.com/maven2</url>
		</repository>
		<repository>
			<id>maven2-repository.dev.java.net</id>
			<name>Java.net Repository for Maven</name>
			<url>http://download.java.net/maven/2/</url>
		</repository>
		<repository>
			<id>maven.jahia.org</id>
			<name>maven.jahia.org</name>
			<url>http://maven.jahia.org/maven2/</url>
		</repository>
		<repository>
			<id>kafeitu</id>
			<url>http://maven.kafeitu.me/nexus/content/groups/public</url>
		</repository>
		<repository>
			<id>Codehaus</id>
			<url>http://repository.codehaus.org</url>
		</repository>
		<!-- <repository> <id>nexus-osc</id> <url>http://maven.oschina.net/content/groups/public/</url> 
			</repository> -->
	</repositories>

	<!-- 设定插件仓库 -->
	<pluginRepositories>

		<pluginRepository>
			<id>jeesite-repos</id>
			<name>Jeesite Repository</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public</url>
		</pluginRepository>

	</pluginRepositories>

	<!-- 依赖项定义 -->
	<dependencies>

		<!-- SPRING begin -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${spring.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
			<version>${spring.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<version>${spring.version}</version>
		</dependency>

		<!-- spring orm -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>${spring.version}</version>
		</dependency>

		<!-- bean validate -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>${validator.version}</version>
		</dependency>
		<!-- SPRING end -->

		<!-- AOP begin -->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.7.4</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.7.4</version>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
			<version>3.1</version>
		</dependency>
		<!-- AOP end -->

		<!-- PERSISTENCE begin -->

		<!-- MyBatis -->
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>${mybatis.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
			<version>${mybatis-spring.version}</version>
		</dependency>

		<!-- connection pool -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>${druid.version}</version>
		</dependency>

		<!-- jdbc driver -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.driver.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc14</artifactId>
			<version>${oracle.driver.version}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/webapp/WEB-INF/lib/ojdbc14-10.2.0.4.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>net.sourceforge.jtds</groupId>
			<artifactId>jtds</artifactId>
			<version>${mssql.driver.version}</version>
			<scope>runtime</scope>
		</dependency>
		<!-- PERSISTENCE end -->

		<!-- WEB begin -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<dependency>
			<groupId>opensymphony</groupId>
			<artifactId>sitemesh</artifactId>
			<version>${sitemesh.version}</version>
		</dependency>
		<dependency>
			<groupId>taglibs</groupId>
			<artifactId>standard</artifactId>
			<version>1.1.2</version>
			<type>jar</type>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
			<type>jar</type>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>jsp-api</artifactId>
			<version>2.1</version>
			<scope>provided</scope>
		</dependency>
		<!-- <dependency> <groupId>javax.servlet</groupId> <artifactId>javax.servlet-api</artifactId> 
			<version>3.0.1</version> <scope>provided</scope> </dependency> <dependency> 
			<groupId>javax.servlet.jsp</groupId> <artifactId>jsp-api</artifactId> <version>2.2</version> 
			<scope>provided</scope> </dependency> -->
		<!-- WEB end -->

		<!-- CACHE begin -->
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache-core</artifactId>
			<version>${ehcache.version}</version>
		</dependency>
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache-web</artifactId>
			<version>${ehcache-web.version}</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.5.1</version>
		</dependency>
		<!-- CACHE end -->

		<!-- SECURITY begin -->
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-cas</artifactId>
			<version>${shiro.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-web</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-ehcache</artifactId>
			<version>${shiro.version}</version>
		</dependency>
		<!-- SECURITY end -->

		<!-- LOGGING begin -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<!-- common-logging 实际调用slf4j -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<!-- java.util.logging 实际调用slf4j -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jul-to-slf4j</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<!-- LOGGING end -->

		<!-- GENERAL UTILS begin -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${commons-lang3.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons-io.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>${commons-codec.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${commons-fileupload.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>${commons-beanutils.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- google java lib -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>

		<!-- jackson json -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-module-jaxb-annotations</artifactId>
			<version>${jackson.version}</version>
		</dependency>

		<!-- fastjson json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>

		<!-- xstream xml -->
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>${xstream.version}</version>
		</dependency>

		<!-- pojo copy -->
		<dependency>
			<groupId>net.sf.dozer</groupId>
			<artifactId>dozer</artifactId>
			<version>${dozer.version}</version>
		</dependency>

		<!-- freemarker engine -->
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>${freemarker.version}</version>
		</dependency>

		<!-- email -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4.7</version>
		</dependency>
		<dependency>
			<groupId>javax.activation</groupId>
			<artifactId>activation</artifactId>
			<version>1.1.1</version>
		</dependency>

		<!-- poi office -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>${poi.version}</version>
		</dependency>

		<!-- image util -->
		<dependency>
			<groupId>com.drewnoakes</groupId>
			<artifactId>metadata-extractor</artifactId>
			<version>2.14.0</version>
		</dependency>

		<!-- 条形码、二维码生成 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>2.2</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>2.2</version>
		</dependency>

		<!-- 中文分词 -->
		<dependency>
			<groupId>org.wltea</groupId>
			<artifactId>analyzer</artifactId>
			<version>2012_u6</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/webapp/WEB-INF/lib/analyzer-2012_u6.jar</systemPath>
		</dependency>
		<!-- GENERAL UTILS end -->

		<!-- TEST begin -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.11</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${spring.version}</version>
		</dependency>
		<!-- TEST end -->

		<!-- User Agent -->
		<dependency>
			<groupId>bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
			<version>1.13</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/webapp/WEB-INF/lib/UserAgentUtils-1.13.jar</systemPath>
		</dependency>

		<!-- JWT -->

		<dependency>
			<groupId>javax</groupId>
			<artifactId>javaee-api</artifactId>
			<version>${version.javaee_api}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
			<version>${version.shiro}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-web</artifactId>
			<version>${version.shiro}</version>
		</dependency>
		<dependency>
			<groupId>org.ops4j.pax.shiro</groupId>
			<artifactId>pax-shiro-cdi-web</artifactId>
			<version>0.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>${version.nimbus-jose-jwt}</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.thetransactioncompany/cors-filter -->
		<dependency>
			<groupId>com.thetransactioncompany</groupId>
			<artifactId>cors-filter</artifactId>
			<version>2.6</version>
		</dependency>
		<!-- 微信服务号 -->
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-mp</artifactId>
			<version>2.9.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-common</artifactId>
			<version>2.9.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-cp</artifactId>
			<version>3.4.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.sd4324530</groupId>
			<artifactId>fastweixin</artifactId>
			<version>1.3.15</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-css</artifactId>
			<version>1.12</version>
		</dependency>

		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>3.0.4</version>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-parsers</artifactId>
			<version>1.24.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-email -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>1.5</version>
		</dependency>
		<!-- 阿里云 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.0.6</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.8</version>
		</dependency>

		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.10</version>
		</dependency>
		<!-- 上传图片到七牛云 -->
		<dependency>
		    <groupId>com.qiniu</groupId>
		    <artifactId>qiniu-java-sdk</artifactId>
		    <version>7.2.0</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>ocr</artifactId>
			<version>1.0.3</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.baidu.aip/java-sdk -->
		<dependency>
			<groupId>com.baidu.aip</groupId>
			<artifactId>java-sdk</artifactId>
			<version>4.12.0</version>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.4.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.aliyun/aliyun-java-sdk-core -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.5.8</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.aliyun/aliyun-java-sdk-imageseg -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-imageseg</artifactId>
			<version>1.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>viapi-utils</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-viapiutils</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.4.2</version>
		</dependency>
        <!-- shedlock -->
	      <dependency>
	         <groupId>net.javacrumbs.shedlock</groupId>
	         <artifactId>shedlock-spring</artifactId>
	         <version>2.3.0</version>
	      </dependency>
	      <dependency>
	         <groupId>net.javacrumbs.shedlock</groupId>
	         <artifactId>shedlock-provider-jdbc-template</artifactId>
	         <version>2.3.0</version>
	      </dependency>

		<!-- 祥云调用工具 -->
		<dependency>
			<groupId>com.cloud</groupId>
			<artifactId>java-sdk-core</artifactId>
			<version>3.0.10</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/webapp/WEB-INF/lib/java-sdk-core-3.0.10.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20160810</version>
		</dependency>
	</dependencies>

	<build>
		<outputDirectory>${project.basedir}/src/main/webapp/WEB-INF/classes/</outputDirectory>
		<plugins>
			<!-- Compiler 插件, 设定JDK版本 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>${jdk.version}</source>
					<target>${jdk.version}</target>
					<showWarnings>true</showWarnings>
				</configuration>
			</plugin>

			<!-- 打包jar文件时，配置manifest文件，加入lib包的jar依赖 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.4</version>
				<!--<configuration> <encoding>${project.build.sourceEncoding}</encoding> 
					</configuration> -->
			</plugin>

			<!-- war 打包插件, 设定war包名称不带版本号 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<packagingExcludes>
						<!-- WEB-INF/classes/com/thinkgem/jeesite/** -->
						WEB-INF/classes/org/apache/ibatis/**,
						WEB-INF/classes/org/mybatis/spring/**
					</packagingExcludes>
					<warSourceExcludes>
						static/bootstrap/2.3.1/docs/**,
						static/ckeditor/samples/**,
						static/compressor*/**,
						static/jquery-jbox/2.3/docs/**,
						static/jquery-jbox/2.3/Skins2/**,
						static/jquery-validation/1.11.0/demo/**,
						static/jquery-ztree/3.5.12/demo/**,
						static/My97DatePicker/docs/**,
						static/supcan/doc/**,
						static/SuperSlide/demo/**,
						static/treeTable/demo/**<!-- , -->
						<!-- userfiles/** --><!-- ,/**/*.jsp -->
						,
						test/**
					</warSourceExcludes>
					<webappDirectory>${project.build.directory}/${project.artifactId}</webappDirectory><!-- 
						<webXml>${project.basedir}/target/jspweb.xml</webXml> -->
					<warName>${project.artifactId}</warName>
				</configuration>
			</plugin>

			<!-- Eclipse 插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<version>2.9</version>
				<configuration>
					<downloadSources>${downloadSources}</downloadSources>
					<downloadJavadocs>false</downloadJavadocs>
					<wtpversion>2.0</wtpversion>
					<jeeversion>5.0</jeeversion>
					<!-- <jeeversion>6.0</jeeversion> -->
					<additionalConfig>
						<file>
							<name>.settings/org.eclipse.core.resources.prefs</name>
							<content>
								<![CDATA[eclipse.preferences.version=1${line.separator}encoding/<project>=${project.build.sourceEncoding}${line.separator}]]>
							</content>
						</file>
					</additionalConfig>
					<additionalProjectnatures>
						<projectnature>org.springframework.ide.eclipse.core.springnature</projectnature>
					</additionalProjectnatures>
				</configuration>
			</plugin>

			<!-- tomcat6插件 -->
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat6-maven-plugin</artifactId>
				<version>${tomcat.version}</version>
				<configuration>
					<port>${webserver.port}</port>
					<path>/${project.artifactId}</path>
					<uriEncoding>${project.build.sourceEncoding}</uriEncoding>
				</configuration>
			</plugin>

			<!-- tomcat7插件 -->
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
				<version>${tomcat.version}</version>
				<configuration>
					<port>${webserver.port}</port>
					<path>/${project.artifactId}</path>
					<uriEncoding>${project.build.sourceEncoding}</uriEncoding>
				</configuration>
			</plugin>

			<!-- jetty插件 -->
			<plugin>
				<groupId>org.mortbay.jetty</groupId>
				<artifactId>jetty-maven-plugin</artifactId>
				<version>${jetty.version}</version>
				<!--<configuration> <connectors> <connector implementation="org.eclipse.jetty.server.nio.SelectChannelConnector"> 
					<port>${webserver.port}</port> </connector> </connectors> <webAppConfig> 
					<contextPath>/${project.artifactId}</contextPath> </webAppConfig> <systemProperties> 
					<systemProperty> <name>org.mortbay.util.URI.charset</name> <value>${project.build.sourceEncoding}</value> 
					</systemProperty> </systemProperties> </configuration> -->
			</plugin>

			<!-- resource插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.7</version>
			</plugin>

			<!-- install插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-install-plugin</artifactId>
				<version>2.5.2</version>
			</plugin>

			<!-- clean插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<version>2.6.1</version>
			</plugin>

			<!-- ant插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.8</version>
			</plugin>

			<!-- dependency插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>2.10</version>
			</plugin>
		</plugins>
	</build>

	<!-- 开发者信息 -->
	<developers>
		<developer>
			<id>thinkgem</id>
			<name>WangZhen</name>
			<email>thinkgem at 163.com</email>
			<roles>
				<role>Project lead</role>
			</roles>
			<timezone>+8</timezone>
		</developer>
	</developers>

</project>

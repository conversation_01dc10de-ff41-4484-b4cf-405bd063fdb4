<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
		http://www.springframework.org/schema/context  http://www.springframework.org/schema/context/spring-context-4.1.xsd"
	default-lazy-init="true">

	<description>Shiro Configuration</description>

    <!-- 加载配置属性文件 -->
	<context:property-placeholder ignore-unresolvable="true" location="classpath:jeesite.properties" />
	<!-- jwt -->
	<context:component-scan base-package="com.github.panchitoboy"><!-- base-package 如果多个，用“,”分隔 -->
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	
	<!-- Shiro权限过滤过滤器定义 -->
	<bean name="shiroFilterChainDefinitions" class="java.lang.String">
		<constructor-arg>
			<value>
				/static/** = anon
				/static/echarts/** = anon
				/static/echarts/chart/** = anon
				/static/upload/** = anon
				/userfiles/** = anon
				${wechatPath}/rest/captcha/** = anon

				${apiPath}/villageApkVersion/** = anon

				${apiPath}/certificate/** = anon
				${apiPath}/member/** = anon
				${apiPath}/app/update/info/** = anon
				${wechatPath}/rest/auth/** = anon
				${wechatPath}/rest/userInfo/** = anon
				${wechatPath}/rest/wechatInfo/** = anon
				${wechatPath}/rest/getWechatPhoneNumber/** = anon
				${wechatPath}/rest/guide/list/** = anon
				${wechatPath}/rest/guide/get/** = anon
				${wechatPath}/rest/video/list/** = anon
                ${wechatPath}/rest/video/get/** = anon
				${adminPath}/bas/certificate/viewScanCode = anon
				${adminPath}/bas/certificate/viewScanCertificateNo = anon
				${adminPath}/bas/certificate/viewDetection = anon
				${wechatPath}/rest/common/getShouyeImgUrl = anon
				${adminPath}/bas/certificate/getAccessTokenForUrl = anon
				/general/** = anon
				${adminPath}/sys/attachment/downloadById = anon
				${adminPath}/sys/attachment/downloadByFileId = anon
				${adminPath}/cas = cas
				${adminPath}/login = authc
				${adminPath}/logout = logout
				${adminPath}/sso = sso
				${adminPath}/** = user
				/act/editor/** = user
				/ReportServer/** = user
				${apiPath}/** = jwt
				${wechatPath}/rest/** = wechat
			</value>
		</constructor-arg>
	</bean>
	
	<!-- 安全认证过滤器 -->
	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" /><!-- 
		<property name="loginUrl" value="${cas.server.url}?service=${cas.project.url}${adminPath}/cas" /> -->
		<property name="loginUrl" value="${adminPath}/login" />
		<property name="successUrl" value="${adminPath}?login" />
		<property name="filters">
            <map>
				<entry key="sso" value-ref="ssoFilter"/>
                <entry key="cas" value-ref="casFilter"/>
                <entry key="authc" value-ref="formAuthenticationFilter"/>
                <entry key="jwt" value-ref="jwtAuthenticationFilter"/>
                <entry key="wechat" value-ref="wechatJwtAuthenticationFilter"/>
            </map>
        </property>
		<property name="filterChainDefinitions">
			<ref bean="shiroFilterChainDefinitions"/>
		</property>
	</bean>
	
	<!-- CAS认证过滤器 -->  
	<bean id="casFilter" class="org.apache.shiro.cas.CasFilter">  
		<property name="failureUrl" value="${adminPath}/login"/>
	</bean>

	<bean id="ssoFilter" class="com.github.panchitoboy.shiro.jwt.filter.SsoFilter">
		<property name="failureUrl" value="/a/login" />
	</bean>

	<bean id="formAuthenticationFilter" class="com.github.panchitoboy.shiro.jwt.filter.FormAuthenticationFilter">  
		<property name="loginUrl" value="${adminPath}/login" />
		<property name="successUrl" value="${adminPath}?login" />
	</bean>
	
	<bean id="jwtAuthenticationFilter" class="com.github.panchitoboy.shiro.jwt.filter.JWTOrFormAuthenticationFilter">  
		<property name="loginUrl" value="${apiPath}/login" />
		<property name="successUrl" value="${apiPath}/?login" />
	</bean>
	
	<bean id="wechatJwtAuthenticationFilter" class="com.github.panchitoboy.shiro.wechat.filter.WechatJWTOrFormAuthenticationFilter">  
        <property name="loginUrl" value="${wechatPath}/rest/login" />
        <property name="successUrl" value="${wechatPath}/rest/login" />
    </bean>
    
	<!-- 定义Shiro安全管理配置 -->
	<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
	    <!-- 认证器 Authenticator的职责是验证用户帐号，是Shiro API中身份验证核心的入口点-->
		<property name="authenticator" ref="authenticator" /> 
		<!-- <property name="realm" ref="systemAuthorizingRealm" /> -->
		<property name="realms"  >
	    	<list>
	        	<bean id="systemAuthorizingRealm" class="com.github.panchitoboy.shiro.jwt.realm.FormAuthorizingRealm" />
	        	<bean id="jwtRealm" class="com.github.panchitoboy.shiro.jwt.realm.JwtRealm" />
	        	<bean id="wechatJwtRealm" class="com.github.panchitoboy.shiro.wechat.realm.WechatJwtRealm" />
				<bean id="ssoRealm" class="com.github.panchitoboy.shiro.jwt.realm.SsoLoginRealm" />
	    	</list>
		</property>
		<property name="sessionManager" ref="sessionManager" />
		<property name="cacheManager" ref="shiroCacheManager" />
	</bean>
	
	<!-- 自定义会话管理配置 -->
	<bean id="sessionManager" class="com.thinkgem.jeesite.common.security.shiro.session.SessionManager"> 
		<property name="sessionDAO" ref="sessionDAO"/>
		
		<!-- 会话超时时间，单位：毫秒  -->
		<property name="globalSessionTimeout" value="${session.sessionTimeout}"/>
		
		<!-- 定时清理失效会话, 清理用户直接关闭浏览器造成的孤立会话   -->
		<property name="sessionValidationInterval" value="${session.sessionTimeoutClean}"/>
<!--  		<property name="sessionValidationSchedulerEnabled" value="false"/> -->
 		<property name="sessionValidationSchedulerEnabled" value="true"/>
 		
		<property name="sessionIdCookie" ref="sessionIdCookie"/>
		<property name="sessionIdCookieEnabled" value="true"/>
		<property name="sessionIdUrlRewritingEnabled" value="false"/>
	</bean>
	
	<!-- 指定本系统SESSIONID, 默认为: JSESSIONID 问题: 与SERVLET容器名冲突, 如JETTY, TOMCAT 等默认JSESSIONID,
		当跳出SHIRO SERVLET时如ERROR-PAGE容器会为JSESSIONID重新分配值导致登录会话丢失! -->
	<bean id="sessionIdCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
	    <constructor-arg name="name" value="jeesite.session.id"/>
	</bean>

	<!-- 自定义Session存储容器 -->
 	<bean id="sessionDAO" class="com.thinkgem.jeesite.common.security.shiro.session.JedisSessionDAO">
 		<property name="sessionIdGenerator" ref="idGen" />
 		<property name="sessionKeyPrefix" value="${redis.keyPrefix}_session_" />
 	</bean>
	<!--<bean id="sessionDAO" class="com.thinkgem.jeesite.common.security.shiro.session.CacheSessionDAO">
		<property name="sessionIdGenerator" ref="idGen" />
		<property name="activeSessionsCacheName" value="activeSessionsCache" />
		<property name="cacheManager" ref="shiroCacheManager" />
	</bean>-->
	
	<!-- 自定义系统缓存管理器-->
 	<bean id="shiroCacheManager" class="com.thinkgem.jeesite.common.security.shiro.cache.JedisCacheManager">
 		<property name="cacheKeyPrefix" value="${redis.keyPrefix}_cache_" />
 	</bean>
	<!--<bean id="shiroCacheManager" class="org.apache.shiro.cache.ehcache.EhCacheManager">
		<property name="cacheManager" ref="cacheManager"/>
	</bean>-->
	
	<!-- 保证实现了Shiro内部lifecycle函数的bean执行 -->
	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
	
	<!-- AOP式方法级权限检查  -->
	<bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator" depends-on="lifecycleBeanPostProcessor">
		<property name="proxyTargetClass" value="true" />
	</bean>
	<bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
    	<property name="securityManager" ref="securityManager"/>
	</bean>
	
	<!-- 认证器 Authenticator的职责是验证用户帐号，是Shiro API中身份验证核心的入口点 -->
	<bean id="authenticator" class="com.github.panchitoboy.shiro.authenticator.ModularRealmAuthenticatorEx">  
	    <property name="authenticationStrategy">  
	        <bean class="org.apache.shiro.authc.pam.FirstSuccessfulStrategy" />  
	    </property>  
	</bean> 
	
</beans>
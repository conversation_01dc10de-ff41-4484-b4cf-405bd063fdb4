/*-----------------------------------------------------------------------------
  版本：v68_20210603
  编写人:lxy
  时间：2021年6月3日15:53:00
  说明：主体表系统字段last_modified赋值逻辑调整
 -----------------------------------------------------------------------------*/
update bas_ent t set t.last_modified=t.update_date where t.last_modified is null;

ALTER TABLE `bas_ent`
MODIFY COLUMN `last_modified`  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `del_flag`;

INSERT INTO `db_version` VALUES ('v68_20210603', 'lxy', '2021-06-03 15:53', '主体表系统字段last_modified赋值逻辑调整');

/*-----------------------------------------------------------------------------
  版本：v69_20210607
  编写人:胡志国
  时间：2021年6月7日10:01:00
  说明：新建机构管辖地区编码
 -----------------------------------------------------------------------------*/
CREATE TABLE `sys_office_area` (
  `office_id` varchar(64) NOT NULL COMMENT '部门id',
  `area_code` varchar(20) NOT NULL COMMENT '地区编码',
  PRIMARY KEY (`office_id`,`area_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机构管辖地区编码表';

INSERT INTO `db_version` VALUES ('v69_20210607', 'huzhiguo', '2021-06-07 10:01', '新增机构管辖地区编码表');

/*-----------------------------------------------------------------------------
  版本：v70_20210610
  编写人:胡志国
  时间：2021年6月10日10:01:00
  说明：新建主城区相关信息包括（机构信息、机构地区编码、用户信息、权限信息）
 -----------------------------------------------------------------------------*/
 -- 主城区机构信息
INSERT INTO `sys_office` VALUES ('852114111633293312', '734067908317020160', '0,636141979318616064,734067908317020160,', '长春市主城区', '30', '220100000000', '220101', '2', '2', '', '', '', '', '', '', '1', null, null, '1', '2021-06-09 09:17:16', '1', '2021-06-10 09:09:38', '', '0');
 -- 主城区地区编码
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220102');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220103');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220104');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220105');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220106');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220171');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220172');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220173');
INSERT INTO `sys_office_area` VALUES ('852114111633293312', '220174');
 -- 主城区用户信息
INSERT INTO `sys_user` VALUES ('852114914070757376', '636141979318616064', '852114111633293312', '220101', '4b2950d6d20661de7b28b424bb7f89c4ac37bdd1236634cdcaf9d75d', '220101', '主城区监管人员', '', '', '', '', null, '192.168.183.113', '2021-06-10 09:03:23', '1', null, '1', '2021-06-09 09:20:27', '1', '2021-06-10 09:03:09', '', '0');
 -- 主城区用户角色
INSERT INTO `sys_user_role` VALUES ('852114914070757376', '831842520022384641');
 -- 修改日常检查菜单
UPDATE `sys_menu` SET href='/bas/check/list/index?checkType=0' WHERE id = '749270420288438272';
 -- 修改专项检查菜单
UPDATE `sys_menu` SET href='/bas/check/list/index?checkType=1' WHERE id = '749270693878693888';

INSERT INTO `db_version` VALUES ('v70_20210610', 'huzhiguo', '2021-06-10 10:01', '新建主城区相关信息包括（机构信息、机构地区编码、用户信息、权限信息）');
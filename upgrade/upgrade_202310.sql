/*-----------------------------------------------------------------------------
  版本：v122_20231010
  编写人:李文才
  时间：2023年10月10日14:26:00
  说明：新增溯源统计菜单和菜单权限
 -----------------------------------------------------------------------------*/
INSERT INTO `sys_menu`(`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('1160611142872072192', '722774558318264320', '0,1,722774558318264320,', '溯源统计', 350, '/bas/scanRecord/traceStatisticIndex', '', '', '1', 'bas:scanRecord:view', '1', '2023-10-08 16:14:20', '1', '2023-10-09 18:01:37', '溯源统计', '0');
INSERT INTO `sys_role_menu`(`role_id`, `menu_id`) VALUES ('734042082313764864', '1160611142872072192');
INSERT INTO `sys_role_menu`(`role_id`, `menu_id`) VALUES ('792700542328504320', '1160611142872072192');
INSERT INTO `sys_role_menu`(`role_id`, `menu_id`) VALUES ('792700757311750144', '1160611142872072192');
INSERT INTO `sys_role_menu`(`role_id`, `menu_id`) VALUES ('831842520022384641', '1160611142872072192');
INSERT INTO `sys_role_menu`(`role_id`, `menu_id`) VALUES ('831842520022384640', '1160611142872072192');

INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v122_20231010', '李文才', '2023-10-10 14:26:00', '新增溯源统计菜单和菜单权限');
/*-----------------------------------------------------------------------------
  版本：v26_20201215
  编写人:lxy
  时间：2020年12月15日14:50:41
  说明：客户信息修改字符集
------------------------------------------------------------------------------*/
ALTER TABLE `bas_member`
MODIFY COLUMN `name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '姓名' AFTER `salt`;

INSERT INTO `db_version` VALUES ('v26_20201215', 'lxy', '2020-12-15 14:50', '客户信息修改字符集');

/*-----------------------------------------------------------------------------
  版本：v27_20201215
  编写人:lxy
  时间：2020年12月15日14:55:41
  说明：创建定时任务分布式锁表
------------------------------------------------------------------------------*/
CREATE TABLE `shedlock` (
  `name` varchar(64) NOT NULL COMMENT '锁名称（name必须是主键）',
  `lock_until` timestamp(3) NULL DEFAULT NULL COMMENT '释放锁时间',
  `locked_at` timestamp(3) NULL DEFAULT NULL COMMENT '获取锁时间',
  `locked_by` varchar(255) DEFAULT NULL COMMENT '锁提供者',
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='定时任务分布式锁表';

INSERT INTO `db_version` VALUES ('v27_20201215', 'lxy', '2020-12-15 14:55', '创建定时任务分布式锁表');

/*-----------------------------------------------------------------------------
  版本：v28_20201225
  编写人:lxy
  时间：2020年12月25日13:55:41
  说明：字典项：新增人参产品分类
------------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('792024552891744256', '33', '人参', 'product_sort_code', '产品分类', NULL, 80, '0', '1', '2020-12-25 13:42:49', '1', '2020-12-25 13:42:49', '', '0');

INSERT INTO `db_version` VALUES ('v28_20201225', 'lxy', '2020-12-25 13:55', '字典项：新增人参产品分类');

/*-----------------------------------------------------------------------------
  版本：v29_20201227
  编写人:lxy
  时间：2020年12月27日11:12:00
  说明：增加省级、市级角色
------------------------------------------------------------------------------*/
INSERT INTO `sys_role` (`id`, `office_id`, `name`, `enname`, `role_type`, `data_scope`, `is_sys`, `useable`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('792700757311750144', '636141979318616064', '市级监管人员', 'cityRole', 'assignment', '2', '1', '1', '1', '2020-12-27 10:29:48', '1', '2020-12-27 10:29:48', '', '0');
INSERT INTO `sys_role` (`id`, `office_id`, `name`, `enname`, `role_type`, `data_scope`, `is_sys`, `useable`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('792700542328504320', '636141979318616064', '省级监管人员', 'provinceRole', 'assignment', '2', '1', '1', '1', '2020-12-27 10:28:57', '1', '2020-12-27 10:28:57', '', '0');

INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '1');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '722774558318264320');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '734042401689042944');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '734373219468836864');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '734374021885329408');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '734374376173993984');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '734463586595766272');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700542328504320', '749233981953671168');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '1');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '722774558318264320');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '734042401689042944');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '734373219468836864');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '734374021885329408');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '734374376173993984');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '734463586595766272');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('792700757311750144', '749233981953671168');

INSERT INTO `db_version` VALUES ('v29_20201227', 'lxy', '2020-12-27 11:12', '增加省级、市级角色');
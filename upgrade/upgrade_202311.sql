/*-----------------------------------------------------------------------------
  版本：v123_20231110
  编写人:LiuBin
  时间：2023年11月10日14:26:00
  说明：新增合格证-产品检测关系表,修改承诺类型名称、排序
 -----------------------------------------------------------------------------*/
CREATE TABLE `bas_certificate_product_inspection` (
                                                      `id` varchar(50) NOT NULL COMMENT '主键id',
                                                      `certificate_id` varchar(50) DEFAULT NULL COMMENT '合格证id',
                                                      `product_inspection_id` varchar(50) DEFAULT NULL COMMENT '检测id',
                                                      `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
                                                      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
                                                      `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                                                      `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
                                                      `del_flag` varchar(1) DEFAULT '0' COMMENT '删除标示',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合格证-产品检测关系表';

UPDATE sys_dict SET label ='自行检测合格' WHERE id ='799636130860367872';
UPDATE sys_dict SET label ='质量安全控制符合要求',sort=20 WHERE id ='927879071612796928';
UPDATE sys_dict SET del_flag ='1' WHERE id ='753660768775307264';

INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `show_village`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('928288313385680897', 8, '承诺达标合格证(生产)', 'https://jlsyncphgzqn.jikeruan.com/img/print-template/77v3.png', '1', '1', '2023-11-17 15:47:03', '1', '2023-11-17 15:47:03', NULL, '0');


INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v123_20231110', 'LiuBin', '2023-11-10 14:26:00', '新增合格证-产品检测关系表,修改承诺类型名称、排序');


/*-----------------------------------------------------------------------------
  版本：v124_20231121
  编写人:LiuBin
  时间：2023年11月21日14:26:00
  说明：删除视频培训,增加索引优化sql
 -----------------------------------------------------------------------------*/

UPDATE `sys_menu` SET del_flag ='1' WHERE id ='735786181949652992';

CREATE INDEX bas_ent_ent_type_IDX USING BTREE ON xm_certificate_test.bas_ent (ent_type);
CREATE INDEX bas_product_ent_id_IDX USING BTREE ON xm_certificate_test.bas_product (ent_id);

UPDATE bas_device_parameter SET `image_url` = 'https://jlsyncphgzqn.jikeruan.com/img/device/E3PLUS-v2.png' WHERE `id` = '1147833982759469056';



INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v124_20231110', 'LiuBin', '2023-12-21 14:26:00', '删除视频培训,增加索引优化sql,历史数据处理');
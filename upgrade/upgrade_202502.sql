
/*-----------------------------------------------------------------------------
  版本：v126_20250213
  编写人:WangLingQing
  时间：2025年02月13日13:20:00
  说明：检测机构部分功能增加
 -----------------------------------------------------------------------------*/

ALTER TABLE `bas_ent` CHANGE `business_type` `business_type` VARCHAR(1) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '主体类型(0:种植；1:养殖；2.机构)';
INSERT INTO sys_dict( id, VALUE, label, TYPE, description, sort, create_by, create_date, update_by, update_date, remarks, del_flag ) VALUES ( '1330923004552544256', '30', '检测机构', 'mainType', '主体类别', 60, '1', '2025-01-20 15:32:51.64', '1', '2025-01-20 15:32:51.64', '', '0' )
    INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`) VALUES ('734091795998179330', '2', '机构', 'businessType', '主体类型', '10', '1', '2025-02-08 11:31:45', '1', '2025-02-08 11:31:45', '');
ALTER TABLE `bas_ent` CHANGE `ent_type` `ent_type` VARCHAR(1) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '主体性质(0:企业；1:个人；2.机构)';
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES('734374376173993993','734373219468836864','0,1,722774558318264320,734373219468836864,','机构主体','80','/bas/ent/list/index?entType=2','','','1','bas:ent:view','1','2020-07-19 11:41:35','1','2020-07-20 10:04:11','','0');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('1','734374376173993993');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('734042082313764864','734374376173993993');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('735792152033886208','734374376173993993');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('792700542328504320','734374376173993993');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('792700757311750144','734374376173993993');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('831842520022384640','734374376173993993');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('831842520022384641','734374376173993993');

INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES('735163171018375173','735152670842552320','0,1,722774558318264320,735152670842552320,','机构主体','80','/bas/ent/examine/list?entType=2','','','1','bas:ent:examine','1','2020-07-21 15:55:59','1','2020-07-21 17:49:55','','0');

INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('1','735163171018375173');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('734042082313764864','735163171018375173');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('831842520022384640','735163171018375173');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('831842520022384641','735163171018375173');

ALTER TABLE `bas_ent` ADD COLUMN `inspection_write_amount` INT(11) DEFAULT 0 NULL COMMENT '上传检测报告次数(检测机构用)' AFTER `certificate_print_date`;
ALTER TABLE `bas_product_inspection` ADD COLUMN `inspection_company_id` VARCHAR(50) NULL COMMENT '检测单位id(检测单位录入数据时填写)' AFTER `inspection_company`;
ALTER TABLE `bas_product_inspection` ADD COLUMN `ent_name` VARCHAR(50) NULL COMMENT '主体名称' AFTER `ent_id`;

INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v126_20250213', 'WangLingQing', '2025-02-13 13:20:00', '检测机构部分功能增加');

INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`) VALUES ('734091542637051910', '2', '检测', 'entType', '主体性质·', '10', '1', '2025-02-13 17:30:10', '1', '2025-02-13 17:30:10', '');
UPDATE `sys_dict` SET `label` = '检测' WHERE `id` = '734091795998179330';
ALTER TABLE `bas_ent` CHANGE `business_type` `business_type` VARCHAR(1) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '主体类型(0:种植；1:养殖；2.检测机构)', CHANGE `ent_type` `ent_type` VARCHAR(1) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '主体性质(0:企业；1:个人；2.检测机构)';
UPDATE `sys_menu` SET `name` = '检测主体' WHERE `id` = '734374376173993993';
UPDATE `sys_menu` SET `name` = '检测主体' WHERE `id` = '735163171018375173';

INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v127_20250214', 'WangLingQing', '2025-02-14 12:00:00', '检测机构字典项调整');

/*----------------------------------------------------------------------------------------------
 @version  v54_20210414
 @description 新建执法人员角色，并分配菜单权限
 <AUTHOR>
 @date 2021/4/14
 -----------------------------------------------------------------------------------------------*/
INSERT INTO sys_role (id,office_id,NAME,enname,role_type,data_scope,create_by,create_date,update_by,update_date, remarks,del_flag,is_sys, useable) VALUES ('831842520022384640', '636141979318616064', '执法人员', 'enforce','user', '8', '1', NOW(), '1', NOW(), '', '0', '1', '1');

insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','1');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','722774558318264320');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','734042401689042944');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','734373219468836864');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','734374021885329408');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','734374376173993984');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','735152670842552320');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','735152731810955264');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','735163171018375168');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','749226085312364544');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','749226341680807936');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','749233981953671168');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','749270175517245440');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','749270420288438272');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','814500298176331776');

INSERT INTO `db_version` VALUES ('v54_20210414', 'my', '2021-04-14 11:00', '新建执法人员角色，并分配菜单权限');

/*------------------------------------------------------------------------------------
 @version  v55_20210415
 @description 执法检查新建检查人员签名、被检查对象签名字段
 <AUTHOR>
 @date 2021/4/15
 -----------------------------------------------------------------------------------*/
ALTER TABLE `bas_check`
  ADD COLUMN `check_user_signature` MEDIUMTEXT NULL   COMMENT '检查人员签名' AFTER `lat`,
  ADD COLUMN `check_behalf_signature` MEDIUMTEXT NULL   COMMENT '被检查对象签名' AFTER `check_user_signature`;
INSERT INTO `db_version` VALUES ('v55_20210415', 'my', '2021-04-15 14:00', '新建检查人员签名、被检查对象签名字段');

/*----------------------------------------------------------------------------
 @version  v56_20210416
 @description 菜单修改（统计报表：新增数据展板、修改统计报表位置）、并分配菜单权限
 <AUTHOR>
 @date 2021/4/16
 ------------------------------------------------------------------------------*/
update sys_menu set href = '', permission = '' where id = '749233981953671168';
INSERT INTO `sys_menu` VALUES ('832543528847409152', '749233981953671168', '0,1,722774558318264320,749233981953671168,', '数据展板', '35', '/bas/statisticReport/display', '', '', '1', 'bas:statisticReport:display', '1', '2021-04-16 09:10:45', '1', '2021-04-16 09:10:45', '数据展板', '0');
INSERT INTO `sys_menu` VALUES ('832543722464870400', '749233981953671168', '0,1,722774558318264320,749233981953671168,', '统计报表', '36', '/bas/statisticReport/view', '', '', '1', 'bas:statisticReport:view', '1', '2021-04-16 09:11:31', '1', '2021-04-16 09:11:31', '统计报表', '0');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','832543528847409152');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','832543722464870400');
INSERT INTO `db_version` VALUES ('v56_20210416', 'huzhiguo', '2021-04-16 14:00', '菜单修改、并分配菜单权限');


/*-----------------------------------------------------------------------------
  版本：v57_20210422
  编写人:lxy
  时间：2021年4月22日15:36:14
  说明：更改行政区划公主岭市编码
------------------------------------------------------------------------------*/
UPDATE basic_sys_area SET del_flag = '1', update_date = now() WHERE CODE = '2227';
UPDATE basic_sys_area SET parent_id = '468732762589757440', parent_ids = '468729585689690112,468732762589757440,' WHERE CODE = '222701';
UPDATE basic_sys_area SET parent_ids = '468729585689690112,468732762589757440,468732762589757aaa,' WHERE CODE LIKE '222701%' AND CODE != '222701';
UPDATE basic_sys_area SET remarks = REPLACE (remarks, '吉林省 公主岭市', '吉林省 长春市') WHERE CODE LIKE '222701%';
UPDATE basic_sys_area SET CODE = REPLACE (CODE, '222701', '220184'), update_date = now() WHERE CODE LIKE '222701%';

-- 删除原公主岭市行政区划
UPDATE sys_area SET del_flag = '1' WHERE id = '222700000000' AND CODE = '2227';
UPDATE sys_area SET del_flag = '1' WHERE id = '222710000000' AND CODE = '222710';
-- 新增公主岭县级行政区划--长春下
INSERT INTO `sys_area` ( `id`, `parent_id`, `parent_ids`, `name`, `code`, `type`, `lng`, `lat`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag` ) VALUES ( '220184000000', '220100000000', '0,000000000000,220000000000,220100000000,', '公主岭市', '220184', '4', NULL, NULL, '200', '1', NOW(), '1', NOW(), NULL, '0' );

INSERT INTO `db_version` VALUES ('v57_20210422', 'lxy', '2021-04-22 15:36', '更改行政区划公主岭市编码');


/*-----------------------------------------------------------------------------
  版本：v58_20210422
  编写人:lxy
  时间：2021年4月22日15:45:00
  说明：业务数据相关公主岭市编码变更
------------------------------------------------------------------------------*/
update bas_certificate t set t.ent_city='2201',t.ent_county='220184' where t.ent_city='2227';
update bas_ent t set t.city='2201',t.county='220184' where t.city='2227';
update sys_office t set t.del_flag='1' where t.area_id='222700000000';
update sys_office t set t.parent_id='770674245440110592',t.parent_ids='0,636141979318616064,770674245440110592,',t.area_id='220184000000',t.code='220184' where t.area_id='222710000000';
update sys_user t set t.office_id='771355189222309888' where t.office_id='771354515373817856';

INSERT INTO `db_version` VALUES ('v58_20210422', 'lxy', '2021-04-22 15:45', '业务数据相关公主岭市编码变更');


/*-----------------------------------------------------------------------------
 @version  v59_20210423
 @description 菜单修改（统计报表排序）、修改线上正式环境长春地区下用户角色
 <AUTHOR>
 @date 2021/4/23
 ------------------------------------------------------------------------------*/
update sys_menu set href = '/bas/statisticReport/view', permission = 'bas:statisticReport:view',sort = 360 where id = '749233981953671168';
update sys_user_role set role_id = '831842520022384640' where user_id = '745573637796397056';
update sys_user_role set role_id = '831842520022384640' where user_id = '770650548230684672';
update sys_user_role set role_id = '831842520022384640' where user_id = '770652899561701376';
update sys_user_role set role_id = '831842520022384640' where user_id = '770653970988269568';
update sys_user_role set role_id = '831842520022384640' where user_id = '770654422047916032';
update sys_user_role set role_id = '831842520022384640' where user_id = '770660633686310912';
update sys_user_role set role_id = '831842520022384640' where user_id = '770663374668169216';
update sys_user_role set role_id = '831842520022384640' where user_id = '770663825589403648';
update sys_user_role set role_id = '831842520022384640' where user_id = '770666707042697216';
update sys_user_role set role_id = '831842520022384640' where user_id = '770667300129865728';
update sys_user_role set role_id = '831842520022384640' where user_id = '770668122188283904';
update sys_user_role set role_id = '831842520022384640' where user_id = '770668625957748736';
update sys_user_role set role_id = '831842520022384640' where user_id = '770669451765874688';
update sys_user_role set role_id = '831842520022384640' where user_id = '770670079292473344';
update sys_user_role set role_id = '831842520022384640' where user_id = '745572307140870144';
update sys_user_role set role_id = '831842520022384640' where user_id = '775793399012786176';
update sys_user_role set role_id = '831842520022384640' where user_id = '771355319346397184';

INSERT INTO `db_version` VALUES ('v59_20210423', 'huzhiguo', '2021-04-23 11:45', '修改统计报表的排序和菜单项，正式环境下长春地区用户角色管理信息');
/*-----------------------------------------------------------------------------
  @version  v60_20210425
  @description 角色添加首页权限和角色名称
  <AUTHOR>
  @date 2021/4/25
--------------------------------------------------------------------------------*/
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES('831842520022384640','734463586595766272');
update sys_role set name = '监管人(长春地区)' where id = '831842520022384640';
INSERT INTO `db_version` VALUES ('v60_20210425', 'huzhiguo', '2021-04-25 11:45', '角色添加首页权限和角色名称');

 /*------------------------------------------------------------------------------
  @version  v61_20210427
  @description 增加app更新版本记录表和菜单
  <AUTHOR>
  @date 2021/4/27
  ------------------------------------------------------------------------------*/
CREATE TABLE `app_update_version` (
  `id` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '编号',
  `version_` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '版本',
  `url_` varchar(1000) COLLATE utf8_bin DEFAULT NULL COMMENT '地址url',
  `force_` varchar(10) COLLATE utf8_bin DEFAULT NULL COMMENT '是否强制更新',
  `platform` varchar(10) COLLATE utf8_bin DEFAULT NULL COMMENT '平台：android,ios',
  `description` varchar(300) COLLATE utf8_bin DEFAULT NULL COMMENT '描述',
  `create_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注信息',
  `del_flag` char(1) COLLATE utf8_bin DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='版本更新记录';
INSERT INTO `sys_menu` VALUES ('836568103503527936', '2', '0,1,2,', '版本更新管理', '35', '', '', '', '1', '', '1', '2021-04-27 11:42:59', '1', '2021-04-27 14:01:28', 'app版本更新', '0');
INSERT INTO `sys_menu` VALUES ('836571480484478976', '836568103503527936', '0,1,2,836568103503527936,', 'app版本更新', '35', '/bas/appUpdateVersion/list', '', '', '1', 'bas:appUpdateVersion:view,bas:appUpdateVersion:edit', '1', '2021-04-27 11:56:24', '1', '2021-04-27 14:01:43', 'app版本更新', '0');

INSERT INTO `db_version` VALUES ('v61_20210427', 'huzhiguo', '2021-04-27 11:45', '增加app更新版本记录表和菜单');
/*------------------------------------------------------------------------------
  @version  v62_20210428
  @description 增加检查内容表、检查指标中添加业务类型字段、添加执法检查内容表、执法检查表新增字段
  <AUTHOR>
  @date 2021/4/28
 --------------------------------------------------------------------------------*/
CREATE TABLE `bas_item_content` (
  `id` varchar(50) NOT NULL COMMENT '主键',
  `target_id` varchar(50) DEFAULT NULL COMMENT '指标Id',
  `content_name` varchar(200) DEFAULT NULL COMMENT '检查项内容名称',
  `model` int(1) DEFAULT NULL COMMENT '类型 0日常，1专项',
  `business_type` varchar(1) DEFAULT NULL COMMENT '业务类型(0:种植；1:养殖)',
  `number` varchar(50) DEFAULT NULL COMMENT '适用地区',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  `sort` int(4) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='指标项目内容表';

ALTER TABLE `bas_item_tar`
  ADD COLUMN `business_type` varchar(1) NULL DEFAULT '' COMMENT '业务类型(0:种植；1:养殖)' AFTER `model`;

CREATE TABLE `bas_check_content` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `check_id` varchar(50) DEFAULT '' COMMENT '检查id',
  `item_tar_id` varchar(50) DEFAULT '' COMMENT '指标id',
  `item_content_id` varchar(50) DEFAULT '' COMMENT '检查内容id',
  `item_content_name` varchar(200) DEFAULT '' COMMENT '检查内容名称',
  `item_check_result_flag` int(5) DEFAULT NULL COMMENT '检查结果 0否，1是',
  `item_tar_sort` int(5) DEFAULT NULL COMMENT '排序',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='执法检查内容信息';

ALTER TABLE `bas_check` ADD COLUMN `business_type` varchar(1) NULL DEFAULT '' COMMENT '业务类型(0:种植；1:养殖)' AFTER `check_behalf_signature`;
ALTER TABLE `bas_check` ADD COLUMN `ent_name` varchar(50) NULL DEFAULT '' COMMENT '主体名称' AFTER `business_type`;
ALTER TABLE `bas_check` ADD COLUMN `ent_scale` varchar(300) NULL DEFAULT '' COMMENT '基地规模' AFTER `ent_name`;
ALTER TABLE `bas_check` ADD COLUMN `contacts` varchar(50) NULL DEFAULT '' COMMENT '联系人' AFTER `ent_scale`;
ALTER TABLE `bas_check` ADD COLUMN `product_name` varchar(600) NULL DEFAULT '' COMMENT '主要品种' AFTER `contacts`;
ALTER TABLE `bas_check` ADD COLUMN `certification_name` varchar(300) NULL DEFAULT '' COMMENT '产品认证名称' AFTER `product_name`;
ALTER TABLE `bas_check` ADD COLUMN `trademark` varchar(300) NULL DEFAULT '' COMMENT '注册商标' AFTER `certification_name`;
ALTER TABLE `bas_check` ADD COLUMN `detail` varchar(150) NULL DEFAULT '' COMMENT '地址' AFTER `trademark`;
ALTER TABLE `bas_check` ADD COLUMN `contacts_phone` varchar(20) NULL DEFAULT '' COMMENT '联系电话' AFTER `detail`;
ALTER TABLE `bas_check` ADD COLUMN `check_opinion` varchar(300) NULL DEFAULT '' COMMENT '检查结果和意见' AFTER `contacts_phone`;
ALTER TABLE `bas_check` ADD COLUMN `quality_contacts` varchar(50) NULL DEFAULT '' COMMENT '质量联系人' AFTER `contacts`;

INSERT INTO `db_version` VALUES ('v62_20210428', 'huzhiguo', '2021-04-28 11:45', '增加指标内容表');




/*-----------------------------------------------------------------------------
  版本：v128_20250625
  编写人:LiJia
  时间：2025年06月25日11:20:00
  说明：检测报告部分功能增加
 -----------------------------------------------------------------------------*/
CREATE TABLE `bas_inspection_report` (
     `id` varchar(50) NOT NULL COMMENT 'id',
     `unique_code` varchar(50) DEFAULT NULL COMMENT '唯一编码',
     `report_number` varchar(50) DEFAULT NULL COMMENT '报告编号',
     `ent_id` varchar(50) NOT NULL COMMENT '受检单位主体id',
     `ent_name` varchar(255) NOT NULL COMMENT '受检单位主体名称',
     `ent_province` varchar(10) DEFAULT NULL COMMENT '受检单位主体-省份',
     `ent_city` varchar(10) DEFAULT NULL COMMENT '受检单位主体-城市',
     `ent_county` varchar(10) DEFAULT NULL COMMENT '受检单位主体-县区',
     `ent_address` varchar(100) DEFAULT NULL COMMENT '受检单位主体(省市县)',
     `ent_detail` varchar(100) DEFAULT NULL COMMENT '受检单位主体详细地址',
     `test_ent_id` varchar(50) NOT NULL COMMENT '委托单位主体id',
     `test_ent_name` varchar(255) NOT NULL COMMENT '委托单位主体名称',
     `result_code` varchar(10) NOT NULL COMMENT '检测结果编码',
     `result_name` varchar(50) NOT NULL COMMENT '检测结果名称',
     `sample_code` varchar(10) NOT NULL COMMENT '受检样品编码',
     `sample_name` varchar(50) NOT NULL COMMENT '受检样品名称',
     `sample_plot` varchar(255) NOT NULL COMMENT '受检样品地块',
     `sample_batch` varchar(255) NOT NULL COMMENT '受检样品批次',
     `sample_number` varchar(255) NOT NULL COMMENT '受检样品编号',
     `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
     `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
     `update_date` datetime DEFAULT NULL COMMENT '更新时间',
     `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
     `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='检测报告表';

alter table bas_inspection_report
    modify sample_plot varchar(255) null comment '受检样品地块';

alter table bas_inspection_report
    modify sample_batch varchar(255) null comment '受检样品批次';

alter table bas_inspection_report
    modify sample_number varchar(255) null comment '受检样品编号';




-- 新建检测报告管理菜单
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('1387399792841195520','722774558318264320','0,1,722774558318264320,','检测报告管理','370','/bas/inspectionReport/list','','','1','bas:inspectionReport:view,bas:inspectionReport:edit','1','2025-06-25 11:51:27','1','2025-07-03 10:35:26','','0');
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('1390285528028413952','722774558318264320','0,1,722774558318264320,','检测报告管理','380','/bas/inspectionReport/list','','','1','bas:inspectionReport:view','1','2025-07-03 10:58:20','1','2025-07-03 10:58:20','','0');
-- 新建检测机构角色
insert into `sys_role` (`id`, `office_id`, `name`, `enname`, `role_type`, `data_scope`, `is_sys`, `useable`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('1390260856398282752','636141979318616064','检测机构','testingOrganization','user','8','1','1','1','2025-07-03 09:20:18','1','2025-07-03 10:35:48','','0');
-- 新建角色和菜单的关联关系
insert into `sys_role_menu` (`role_id`, `menu_id`) values('1390260856398282752','1387399792841195520');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('734042082313764864','1390285528028413952');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('792700542328504320','1390285528028413952');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('792700757311750144','1390285528028413952');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','1390285528028413952');
-- 新建字典
INSERT INTO `sys_dict`( `id`, `value`, `label`, `type`, `description`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag` )VALUES ( '1387465684199407616', '02', '有风险项', 'resultCode', '检测结果编码', '20', '1', '2025-06-25 16:13:16', '1', '2025-06-25 16:13:16', '', '0' );
INSERT INTO `sys_dict`( `id`, `value`, `label`, `type`, `description`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag` )VALUES (  '1387465460068384768', '01', '全部合格', 'resultCode', '检测结果编码', '10', '1', '2025-06-25 16:12:23', '1', '2025-06-25 16:12:23', '', '0');
INSERT INTO `sys_dict`( `id`, `value`, `label`, `type`, `description`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag` )VALUES (  '1387478946404106240', '02', '西洋参', 'sampleCode', '受检样品编码', '20', '1', '2025-06-25 17:05:58', '1', '2025-06-25 17:05:58', '', '0');
INSERT INTO `sys_dict`( `id`, `value`, `label`, `type`, `description`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag` )VALUES (  '1387478787746168832', '01', '人参', 'sampleCode', '受检样品编码', '10', '1', '2025-06-25 17:05:21', '1', '2025-06-25 17:05:21', '', '0');
-- 新建索引
CREATE INDEX index_att_tableid ON sys_attachment(table_id);
CREATE INDEX index_att_tablename ON sys_attachment(table_name);
-- 插入数据库版本记录
INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v128_20250625', 'FengBingQi', '2025-02-13 13:20:00', '新增检测报告管理功能');
INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v129_20250707', 'FengBingQi', '2025-07-07 14:56:00', '检测报告管理菜单与角色绑定');
-- 新建角色和菜单的关联关系
insert into `sys_role_menu` (`role_id`, `menu_id`) values('1390260856398282752','1');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('1390260856398282752','722774558318264320');

/*-----------------------------------------------------------------------------
  版本：v87_20211014
  编写人:lxy
  时间：2021年10月14日14:48:44
  说明：新增字典项：保存条件
 -----------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('897786624190971904', '3', '冷冻', 'storageTypeCode', '保存条件', NULL, 30, '0', '1', '2021-10-13 10:03:31', '1', '2021-10-13 10:03:31', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('897786587432091648', '2', '常温', 'storageTypeCode', '保存条件', NULL, 20, '0', '1', '2021-10-13 10:03:23', '1', '2021-10-13 10:03:23', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('897786549498806272', '1', '冷藏', 'storageTypeCode', '保存条件', NULL, 10, '0', '1', '2021-10-13 10:03:13', '1', '2021-10-13 10:03:13', '', '0');

INSERT INTO `db_version` VALUES ('v87_20211014', 'lxy', '2021-10-14 14:48:44', '新增字典项：保存条件');

/*-----------------------------------------------------------------------------
  版本：v88_20211014
  编写人:lxy
  时间：2021年10月14日14:52:03
  说明：新增表：产品保质方式表
 -----------------------------------------------------------------------------*/
CREATE TABLE `bas_product_storage` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `ent_id` varchar(50) DEFAULT '' COMMENT '主体id',
  `product_id` varchar(50) DEFAULT '' COMMENT '产品id',
  `storage_type_code` varchar(50) DEFAULT '' COMMENT '保存条件code',
  `storage_type_name` varchar(50) DEFAULT '' COMMENT '保存条件name',
  `storage_days` varchar(50) DEFAULT '' COMMENT '保存时间',
  `storage_unit` varchar(50) DEFAULT '' COMMENT '保存单位',
  `instruction` varchar(100) DEFAULT '' COMMENT '补充说明',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产品保质方式表';

INSERT INTO `db_version` VALUES ('v88_20211014', 'lxy', '2021-10-14 14:52:03', '新增表：产品保质方式表');

/*-----------------------------------------------------------------------------
  版本：v89_20211018
  编写人:py
  时间：2021年10月18日10:12:03
  说明：行政区划5级code变为9位对应系统的升级脚本
 -----------------------------------------------------------------------------*/
 -- 将行政区划的5级code长度变成9位，舍弃后面的000
UPDATE sys_area SET CODE=LEFT(CODE,9) WHERE TYPE='5';

-- 历史数据修改
UPDATE `bas_ent` SET town=LEFT(town,9) WHERE town!='' OR town IS NOT NULL;
INSERT INTO `db_version` VALUES ('v89_20211018', 'py', '2021-10-18 10:52:03', '行政区划5级code变为9位对应系统的升级脚本');


/*-----------------------------------------------------------------------------
  版本：v90_20211020
  编写人:py
  时间：2021年10月20日10:51:03
  说明：乡村版合格证 增加apk版本表
 -----------------------------------------------------------------------------*/
CREATE TABLE `bas_village_apk_version` (
  `id` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '编号',
  `version_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '版本名称',
  `version_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '版本编号',
  `operate_date` datetime DEFAULT NULL COMMENT '版本时间',
  `version_describe` varchar(300) COLLATE utf8_bin DEFAULT NULL COMMENT '描述',
  `create_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注信息',
  `del_flag` char(1) COLLATE utf8_bin DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='乡村合格证apk版本表';

INSERT INTO `db_version` VALUES ('v90_20211020', 'py', '2021-10-20 10:51:03', '乡村版合格证增加apk版本表');
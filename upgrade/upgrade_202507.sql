ALTER TABLE `bas_inspection_report` ADD COLUMN `sampling_number` varchar(50) NULL COMMENT '抽样单号' AFTER `sample_number`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `sample_date` datetime NULL COMMENT '样品到样日期' AFTER `sampling_number`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `contacts` varchar(50) NULL COMMENT '联系人' AFTER `sample_date`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `mobile` varchar(50) NULL COMMENT '联系电话' AFTER `contacts`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `ent_type` varchar(5) NULL COMMENT '受检单位性质' AFTER `ent_detail`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `is_secure_county` varchar(1) NULL COMMENT '是否是安全县' AFTER `mobile`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `growth_years` varchar(50) NULL COMMENT '生长年限' AFTER `is_secure_county`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `planting_area` varchar(50) NULL COMMENT '种植面积(亩)' AFTER `growth_years`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `ginseng_production` varchar(50) NULL COMMENT '人参产量(斤)' AFTER `planting_area`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `is_expire` varchar(1) NULL COMMENT '是否过药品安全间隔期' AFTER `planting_area`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `sampling_date` datetime NULL COMMENT '抽样日期' AFTER `sampling_number`;
ALTER TABLE `bas_inspection_report` ADD COLUMN `sampling_person` varchar(50) NULL COMMENT '抽样人' AFTER `sampling_date`;
ALTER TABLE `bas_inspection_report` MODIFY COLUMN `ent_id` varchar(50) NULL COMMENT '受检单位主体id';
ALTER TABLE `bas_inspection_report` MODIFY COLUMN `remarks` varchar(300) NULL COMMENT '备注';

alter table bas_product
    add save_type varchar(2) default '0' null comment '0-代表用户录入 1-代表sql录入' after re_buy_visible;

alter table bas_product_inspection
    add report_id varchar(50) null comment '检验报告主键ID';

-- 新增人参西洋参sql
INSERT INTO bas_product (
    id,
    name,
    ent_id,
    product_sort_code,
    product_sort_name,
    province,
    city,
    county,
    address,
    detail,
    save_type,
    del_flag
)
SELECT
    product_data.data_id,
    product_data.name,
    product_data.ent_id,
    product_data.product_sort_code,
    product_data.product_sort_name,
    product_data.province,
    product_data.city,
    product_data.county,
    product_data.address,
    product_data.detail,
    product_data.save_type,
    product_data.del_flag
FROM (
         -- 人参
         SELECT
             UUID_SHORT() AS data_id,
             '人参' AS name,
             a.id AS ent_id,
             '33' AS product_sort_code,
             '人参' AS product_sort_name,
             IFNULL(a.province, '') AS province,
             IFNULL(a.city, '') AS city,
             IFNULL(a.county, '') AS county,
             IFNULL(a.address, '') AS address,
             IFNULL(a.detail, '') AS detail,
             '1' AS save_type,
             '0' as del_flag
         FROM bas_ent a
                  LEFT JOIN bas_product p ON p.ent_id = a.id AND p.del_flag = 0
         WHERE a.ent_type IN ('0','1')
           AND a.examine_status = '1'
           AND a.frozen_flag = '0'
           AND a.del_flag = '0'
           AND p.product_sort_name = '人参'
         GROUP BY a.id, a.province, a.city, a.county, a.address, a.detail

         UNION ALL

         -- 西洋参
         SELECT
             UUID_SHORT() AS data_id,
             '西洋参' AS name,
             a.id AS ent_id,
             '33' AS product_sort_code,
             '人参' AS product_sort_name,
             IFNULL(a.province, '') AS province,
             IFNULL(a.city, '') AS city,
             IFNULL(a.county, '') AS county,
             IFNULL(a.address, '') AS address,
             IFNULL(a.detail, '') AS detail,
             '1' AS save_type,
             '0' as del_flag
         FROM bas_ent a
                  LEFT JOIN bas_product p ON p.ent_id = a.id AND p.del_flag = 0
         WHERE a.ent_type IN ('0','1')
           AND a.examine_status = '1'
           AND a.frozen_flag = '0'
           AND a.del_flag = '0'
           AND p.product_sort_name = '人参'
         GROUP BY a.id, a.province, a.city, a.county, a.address, a.detail
     ) AS product_data
ORDER BY product_data.ent_id, product_data.name;

-- 数据库版本
INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v130_20250731', 'FengBingQi', now(), '检测报告管理新增字段，新增西洋参和人参产品');


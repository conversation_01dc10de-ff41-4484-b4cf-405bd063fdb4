
/*-----------------------------------------------------------------------------
  版本：v125_20240116
  编写人:LiuBin
  时间：2024年01月16日14:26:00
  说明：产品表增加复购商品id、复购开关显示字段、合格证表增加复购开关显示字段
 -----------------------------------------------------------------------------*/

ALTER TABLE `bas_product`
    ADD COLUMN `re_buy_product_id`  varchar(100) NULL  COMMENT '复购商品id' AFTER `print_date`,
    ADD COLUMN `re_buy_visible`  varchar(1) NULL  COMMENT '复购按钮是否显示1显示,其他不显示' AFTER `re_buy_product_id`,
    ADD COLUMN `re_buy_product_name`  varchar(200) NULL  COMMENT '复购商品名称' AFTER `re_buy_visible`;

ALTER TABLE `bas_certificate` ADD COLUMN `re_buy_visible` varchar(1) NULL COMMENT '复购按钮是否显示1显示,其他不显示' AFTER `product_inspection_id`;
ALTER TABLE bas_product MODIFY COLUMN re_buy_product_name VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE bas_certificate MODIFY COLUMN product_introduction VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

INSERT INTO `db_version`(`VERSION_ID`, `OPERATOR`, `OPERATE_DATE`, `VERSION_DESCRIBE`) VALUES ('v125_20240116', 'LiuBin', '2024-01-16 14:26:00', '产品表增加复购商品id、复购开关显示字段、合格证表增加复购开关显示字段');
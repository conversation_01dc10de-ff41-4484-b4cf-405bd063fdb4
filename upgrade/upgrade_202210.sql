/*-----------------------------------------------------------------------------
  版本：v119_20221027
  编写人:lxy
  时间：2022年10月27日14:24:00
  说明：系统角色名称修改
 -----------------------------------------------------------------------------*/
UPDATE `sys_role` SET `name`='执法人员' WHERE (`id`='831842520022384640');
UPDATE `sys_role` SET `name`='监管人员' WHERE (`id`='831842520022384641');

INSERT INTO `db_version` VALUES ('v119_20221027', 'lxy', '2022-10-27 14:24', '系统角色名称修改');

/*-----------------------------------------------------------------------------
  版本：v120_20221027
  编写人:lxy
  时间：2022年10月27日14:26:00
  说明：修改短信备注字段长度
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_verification_code`
MODIFY COLUMN `remarks`  varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '备注信息' AFTER `update_date`;

INSERT INTO `db_version` VALUES ('v120_20221027', 'lxy', '2022-10-27 14:26', '修改短信备注字段长度');

/*-----------------------------------------------------------------------------
 @version  v63_20210508
 @description 修改原有监管人员角色名称为执法人员（长春地区）、删除多余权限、新增监管人员（长春地区）设置菜单、长春是用户权限修改
 <AUTHOR>
 @date 2021/5/08
 -----------------------------------------------------------------------------*/
update sys_role set name = '执法人员(长春地区)' where id = '831842520022384640';
delete from `sys_role_menu` where role_id = '831842520022384640' and (menu_id='749226085312364544' or menu_id='749226341680807936');
insert into sys_role (id,office_id,NAME,enname,role_type,data_scope,create_by,create_date,update_by,update_date, remarks,del_flag,is_sys, useable) VALUES ('831842520022384641', '636141979318616064', '监管人员(长春地区)', 'supervisor','user', '8', '1', NOW(), '1', NOW(), '', '0', '1', '1');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','1');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','722774558318264320');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','734042401689042944');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','734373219468836864');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','734374021885329408');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','734374376173993984');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','749226085312364544');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','749226341680807936');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','749233981953671168');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','749270175517245440');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','749270420288438272');
insert into `sys_role_menu` (`role_id`, `menu_id`) VALUES('831842520022384641','734463586595766272');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','832543528847409152');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','832543722464870400');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384640','749260409495617536');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','749260409495617536');
--2021年5月14日长春地区（监管人员）主体审核权限 开始
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','735152670842552320');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','735152731810955264');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','735163171018375168');
insert into `sys_role_menu` (`role_id`, `menu_id`) values('831842520022384641','814500298176331776');
--2021年5月14日长春地区（监管人员）主体审核权限 开始
update sys_user_role set role_id = '831842520022384641' where user_id = '745572307140870144';       --修改长春市用户为监管人员（长春地区）
INSERT INTO `db_version` VALUES ('v63_20210508', 'huzhiguo', '2021-05-08 11:45', '增加角色长春地区监管人员');

/*-----------------------------------------------------------------------------
 @version  v64_20210511
 @description 检查记录中添加字段：存栏量、年出栏量、动物防疫条件合格证
 <AUTHOR>
 @date 2021/5/11
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_check` ADD COLUMN `animal_certificate_no` varchar(100) NULL DEFAULT '' COMMENT '动物防疫条件合格证' AFTER `quality_contacts`;
ALTER TABLE `bas_check` ADD COLUMN `livestock_scale` int NULL DEFAULT 0 COMMENT '存栏量' AFTER `animal_certificate_no`;
ALTER TABLE `bas_check` ADD COLUMN `out_scale` int NULL DEFAULT 0 COMMENT '年出栏量' AFTER `livestock_scale`;
INSERT INTO `db_version` VALUES ('v64_20210511', 'huzhiguo', '2021-05-11 11:45', '检查记录中添加字段：存栏量、年出栏量、动物防疫条件合格证');

/*-----------------------------------------------------------------------------
  版本：v65_20210512
  编写人:lxy
  时间：2021年5月12日15:20:55
  说明：主体新增字段冻结标示
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_ent`
ADD COLUMN `frozen_flag`  varchar(1) NULL DEFAULT '0' COMMENT '冻结标识 0:否 1:是' AFTER `change_opinion`;

INSERT INTO `db_version` VALUES ('v65_20210512', 'lxy', '2021-05-12 15:20', '主体新增字段冻结标示');

/*-----------------------------------------------------------------------------
  版本：v66_20210513
  编写人:lxy
  时间：2021年5月13日15:15:55
  说明：系统管理员角色增加菜单
 -----------------------------------------------------------------------------*/
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('1', '734042401689042944');

INSERT INTO `db_version` VALUES ('v66_20210513', 'lxy', '2021-05-13 15:15', '系统管理员角色增加菜单');

/*-----------------------------------------------------------------------------
  版本：v67_20210520
  编写人:lxy
  时间：2021年5月20日09:23:00
  说明：系统管理员角色增加菜单
 -----------------------------------------------------------------------------*/
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('1', '734373219468836864');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('1', '734374021885329408');
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('1', '734374376173993984');

INSERT INTO `db_version` VALUES ('v67_20210520', 'lxy', '2021-05-20 09:23', '系统管理员角色增加菜单');
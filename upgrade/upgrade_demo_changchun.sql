/*-----------------------------------------------------------------------------
  编写人:张明月
  时间：2020年8月29日11:05:55
  说明：指标表
------------------------------------------------------------------------------*/

CREATE TABLE `bas_item_tar` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `target_id` VARCHAR(50) DEFAULT NULL COMMENT '指标Id',
  `parent_id` VARCHAR(50) DEFAULT NULL COMMENT '父Id',
  `type_name` VARCHAR(200) DEFAULT NULL COMMENT '检查项名称',
  `model` INT(1) DEFAULT NULL COMMENT '类型 0日常，1专项',
  `number` VARCHAR(50) DEFAULT NULL COMMENT '适用地区',
  `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
  `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  `remarks` VARCHAR(225) DEFAULT NULL COMMENT '备注',
  `del_flag` VARCHAR(1) DEFAULT NULL COMMENT '删除标示',
  `sort` INT(4) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='指标表';

/*-----------------------------------------------------------------------------
  编写人:张明月
  时间：2020年8月29日11:22:55
  说明：指标维护菜单
------------------------------------------------------------------------------*/
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749226085312364544','722774558318264320','0,1,722774558318264320,','指标维护','240','','','','1','','1','2020-08-29 11:16:59','1','2020-08-29 11:16:59','','0');
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749228417139867648','749226085312364544','0,1,722774558318264320,749226085312364544,','专项指标维护','60','/bas/itemTar/list?model=1','','','1','bas:itemTar:view,bas:itemTar:edit','1','2020-08-29 11:26:15','1','2020-08-29 11:26:28','','0');
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749226341680807936','749226085312364544','0,1,722774558318264320,749226085312364544,','日常指标维护','30','/bas/itemTar/list?model=0','','','1','bas:itemTar:view,bas:itemTar:edit','1','2020-08-29 11:18:00','1','2020-08-29 11:25:58','','0');

/*-----------------------------------------------------------------------------
  编写人:张明月
  时间：2020年8月29日13:29:55
  说明：信息发布表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_notice` (
  `id` VARCHAR(50) NOT NULL COMMENT 'id',
  `title` VARCHAR(50) DEFAULT NULL COMMENT '标题',
  `content` MEDIUMTEXT COMMENT '内容',
  `edit_date` DATETIME DEFAULT NULL COMMENT '编辑时间',
  `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
  `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  `remarks` VARCHAR(225) DEFAULT NULL COMMENT '备注',
  `del_flag` VARCHAR(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='信息发布表';

/*-----------------------------------------------------------------------------
  编写人:张明月
  时间：2020年8月29日13:35:55
  说明：信息发布表菜单
------------------------------------------------------------------------------*/
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749260409495617536','722774558318264320','0,1,722774558318264320,','信息发布','270','/bas/notice/list','','','1','bas:notice:view,bas:notice:edit','1','2020-08-29 13:33:22','1','2020-08-29 13:34:35','','0');


/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2020年8月29日13:35:55
  说明：创建检查表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_check` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `ent_id` varchar(50) DEFAULT '' COMMENT '主体id',
  `check_type` int(10) DEFAULT NULL COMMENT '检查类型 0日常,1专项',
  `check_result_flag` int(10) DEFAULT NULL COMMENT '检查结果 0不合格,1合格',
  `check_date` datetime DEFAULT NULL COMMENT '检查时间',
  `check_user_id` varchar(50) DEFAULT '' COMMENT '检查人员id',
  `check_user_name` varchar(50) DEFAULT '' COMMENT '检查人员姓名',
  `check_address` varchar(100) DEFAULT '' COMMENT '检查地点',
  `check_retinue` varchar(50) DEFAULT '' COMMENT '检查随行人员',
  `check_item_amount` int(10) DEFAULT NULL COMMENT '检查项目数量',
  `lng` varchar(50) DEFAULT '' COMMENT '经度',
  `lat` varchar(50) DEFAULT '' COMMENT '纬度',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='执法检查';


CREATE TABLE `bas_check_item` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `check_id` varchar(50) DEFAULT '' COMMENT '检查id',
  `item_tar_id` varchar(50) DEFAULT '' COMMENT '指标id',
  `item_tar_name` varchar(100) DEFAULT '' COMMENT '指标名称',
  `item_check_result_flag` int(5) DEFAULT NULL COMMENT '检查结果 0否，1是',
  `item_tar_sort` int(5) DEFAULT NULL COMMENT '排序',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='执法检查';

/*-----------------------------------------------------------------------------
  编写人:张明月
  时间：2020年8月29日15:03:55
  说明：信息反馈表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_feedback` (
  `id` VARCHAR(50) NOT NULL COMMENT 'id',
  `member_id` VARCHAR(50) DEFAULT NULL COMMENT '小程序登录人id',
  `phone` VARCHAR(50) DEFAULT NULL COMMENT '联系电话',
  `content` VARCHAR(500) DEFAULT NULL COMMENT '反馈内容',
  `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
  `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  `remarks` VARCHAR(225) DEFAULT NULL COMMENT '备注',
  `del_flag` VARCHAR(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='反馈信息表';
insert into `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749283861757689856','722774558318264320','0,1,722774558318264320,','意见反馈','330','/bas/feedback/list','','','1','bas:feedback:view,bas:feedback:edit','1','2020-08-29 15:06:34','1','2020-08-29 16:21:08','','0');

/*-----------------------------------------------------------------------------
  编写人:mengying
  时间：2020年8月31日18:13
  说明：增加检查结果字典项
------------------------------------------------------------------------------*/
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749282177748828160','0','不合格','checkResult','检查结果',NULL,'10','0','1','2020-08-29 14:59:52','1','2020-08-29 14:59:52','','0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('749282327321903104','1','合格','checkResult','检查结果',NULL,'10','0','1','2020-08-29 15:00:28','1','2020-08-29 15:00:28','','0');

/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2020年8月31日18:13
  说明：增加菜单
------------------------------------------------------------------------------*/
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('749270693878693888', '749270175517245440', '0,1,722774558318264320,749270175517245440,', '专项检查', 60, '/bas/check/getCheckList?checkType=1', '', '', '1', 'bas:check:view', '1', '2020-8-29 14:14:14', '1', '2020-8-29 15:11:30', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('749270420288438272', '749270175517245440', '0,1,722774558318264320,749270175517245440,', '日常检查', 30, '/bas/check/getCheckList?checkType=0', '', '', '1', 'bas:check:view', '1', '2020-8-29 14:13:09', '1', '2020-8-29 15:11:20', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('749270175517245440', '722774558318264320', '0,1,722774558318264320,', '监督检查', 300, '', '', '', '1', '', '1', '2020-8-29 14:12:11', '1', '2020-8-29 14:12:11', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('749233981953671168', '722774558318264320', '0,1,722774558318264320,', '统计报表', 240, '/bas/statisticReport/view', '', '', '1', 'bas:statisticReport:view', '1', '2020-8-29 11:48:21', '1', '2020-8-29 11:48:57', '统计报表', '0');

/*-----------------------------------------------------------------------------
  编写人:zyj
  时间：2020年9月10日9:14
  说明：合格证增加字段
------------------------------------------------------------------------------*/
ALTER TABLE bas_certificate
ADD COLUMN `inspection_situation` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检测情况' AFTER `end_serial_number`;
ALTER TABLE bas_certificate
ADD COLUMN `sample_no` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '当前产品样品编号' AFTER `inspection_situation`;
/*-----------------------------------------------------------------------------
  编写人:zyj
  时间：2020年9月10日9:14
  说明：产品增加字段
------------------------------------------------------------------------------*/
ALTER TABLE bas_product
ADD COLUMN `current_sample_no` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最新样品编号' AFTER `product_introduction`;
ALTER TABLE bas_product
ADD COLUMN `query_code_url` VARCHAR(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '二维码url' AFTER `current_sample_no`;
/*-----------------------------------------------------------------------------
  编写人:zyj
  时间：2020年9月10日9:14
  说明：产品检测记录表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_product_record` (
  `id` VARCHAR(50) NOT NULL COMMENT 'id',
  `product_id` VARCHAR(50) DEFAULT NULL COMMENT '产品id',
  `sample_name` VARCHAR(50) DEFAULT NULL COMMENT '样品名称',
  `sample_no` VARCHAR(50) DEFAULT NULL COMMENT '样品编号',
  `query_code_url` VARCHAR(300) DEFAULT NULL COMMENT '二维码url',
  `create_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `create_date` DATETIME DEFAULT NULL COMMENT '创建时间',
  `update_by` VARCHAR(50) DEFAULT NULL COMMENT '更新者',
  `update_date` DATETIME DEFAULT NULL COMMENT '更新时间',
  `remarks` VARCHAR(225) DEFAULT NULL COMMENT '备注',
  `del_flag` VARCHAR(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='产品检测记录表';
/*-----------------------------------------------------------------------------
  编写人:zyj
  时间：2020年9月11日14:38
  说明：增加检测情况字典项
------------------------------------------------------------------------------*/
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('753660768775307264','is01','无检测信息','inspection_situation_code','检测情况',NULL,'1','0','1','2020-09-10 16:58:50','1','2020-09-10 16:58:50','','0');
insert into `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('753660953416957952','is02','委托检测合格','inspection_situation_code','检测情况',NULL,'2','0','1','2020-09-10 16:59:34','1','2020-09-10 16:59:34','','0');


/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2020年9月17日14:29:08
  说明：检查表增加区域编码code
------------------------------------------------------------------------------*/
ALTER TABLE `bas_check`
ADD COLUMN `area_code`  varchar(20) NULL DEFAULT '' COMMENT '行政区划编码' AFTER `ent_id`;

/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2020年9月17日15:29:08
  说明：检测情况字典项:委托检测合格->自检合格
------------------------------------------------------------------------------*/
UPDATE `sys_dict` SET `label`='自检合格' WHERE (`id`='753660953416957952');

/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2020年9月17日15:29:08
  说明：检测情况字典项添加:委托检测合格
------------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('756177591244685312', 'is03', '委托检测合格', 'inspection_situation_code', '检测情况', NULL, 12, '0', '1', '2020-9-17 15:39:47', '1', '2020-9-17 15:39:47', '', '0');

/*-----------------------------------------------------------------------------
  版本：v49_20210302
  编写人:lxy
  时间：2021年3月2日13:43:00
  说明：添加新表:主体信息变更表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_ent_change` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `ent_id` varchar(50) DEFAULT '' COMMENT '主体id',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `business_type` varchar(1) DEFAULT NULL COMMENT '主体类型(0:种植；1:养殖)',
  `ent_type` varchar(1) DEFAULT NULL COMMENT '主体性质(0:企业；1:个人)',
  `main_type` varchar(2) DEFAULT '' COMMENT '主体类别',
  `farm_type` varchar(1) DEFAULT '' COMMENT '养殖分类 (0:牧业 1:渔业)',
  `social_code` varchar(50) DEFAULT NULL COMMENT '统一社会信用代码',
  `card_no` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `legal_person` varchar(50) DEFAULT NULL COMMENT '法人',
  `contacts` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contacts_phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `province` varchar(2) DEFAULT NULL COMMENT '省',
  `city` varchar(4) DEFAULT NULL COMMENT '市',
  `county` varchar(6) DEFAULT NULL COMMENT '县区',
  `address` varchar(100) DEFAULT NULL COMMENT '地址(省市县)',
  `detail` varchar(150) DEFAULT NULL COMMENT '详细地址',
  `lng` varchar(100) DEFAULT NULL COMMENT '经度',
  `lat` varchar(100) DEFAULT NULL COMMENT '纬度',
  `company_introduction` varchar(500) DEFAULT NULL COMMENT '企业介绍',
  `ent_honor` varchar(500) DEFAULT NULL COMMENT '荣誉简介',
  `submit_date` datetime DEFAULT NULL COMMENT '提交时间',
  `examine_status` varchar(2) DEFAULT NULL COMMENT '审核状态（0：待审核；1：通过,-1驳回）',
  `examine_man` varchar(50) DEFAULT NULL COMMENT '审核人',
  `examine_opinion` varchar(200) DEFAULT NULL COMMENT '审核意见',
  `examine_date` datetime DEFAULT NULL COMMENT '审核时间',
  `autograph` mediumtext COMMENT '电子签名',
  `basic_flag` varchar(1) DEFAULT '' COMMENT '基础信息采集对接标示(0:否 1:是)',
  `basic_enter_flag` varchar(1) DEFAULT '' COMMENT '基础信息录入标示(0:否 1:是)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='主体信息变更表';

INSERT INTO `db_version` VALUES ('v49_20210302', 'lxy', '2021-03-02 13:43', '添加新表:主体信息变更表');

/*-----------------------------------------------------------------------------
  版本：v50_20210306
  编写人:lxy
  时间：2021年3月6日14:16:00
  说明：修改表:主体信息增加字段
------------------------------------------------------------------------------*/
ALTER TABLE `bas_ent`
ADD COLUMN `change_status`  varchar(2) NULL DEFAULT '' COMMENT '变更业务状态' AFTER `certificate_print_amount`,
ADD COLUMN `change_view_flag`  varchar(1) NULL DEFAULT '' COMMENT '变更后查看标识 0:否 1:是' AFTER `change_status`,
ADD COLUMN `change_opinion`  varchar(200) NULL DEFAULT '' COMMENT '变更业务意见' AFTER `change_view_flag`;

INSERT INTO `db_version` VALUES ('v50_20210306', 'lxy', '2021-03-02 13:43', '修改表:主体信息增加字段');

/*-----------------------------------------------------------------------------
  版本：v51_20210323
  编写人:lxy
  时间：2021年3月23日16:36:00
  说明：基础信息表:经营状态设置默认值生产
------------------------------------------------------------------------------*/
ALTER TABLE `bas_basic_ent`
MODIFY COLUMN `produce_flag`  int(11) NULL DEFAULT 1 COMMENT '0停产,1生产' AFTER `permit_no`;

INSERT INTO `db_version` VALUES ('v51_20210323', 'lxy', '2021-03-23 16:36', '基础信息表:经营状态设置默认值生产');

/*-----------------------------------------------------------------------------
  版本：v52_20210330
  编写人:lxy
  时间：2021年3月30日19:08:00
  说明：主体表：增加系统更新时间字段、且设置update_date为初始值
------------------------------------------------------------------------------*/
ALTER TABLE `bas_ent`
ADD COLUMN `last_modified`  timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `del_flag`;

update bas_ent t set t.last_modified=t.update_date;

INSERT INTO `db_version` VALUES ('v52_20210330', 'lxy', '2021-03-30 19:08', '主体表：增加系统更新时间字段、且设置update_date为初始值');

/*-----------------------------------------------------------------------------
  版本：v53_20210331
  编写人:lxy
  时间：2021年3月31日10:00:00
  说明：新建kettle同步业务表，并赋初始值
------------------------------------------------------------------------------*/
CREATE TABLE `kettle_export_table` (
  `table_name` varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '需要etl的表名',
  `all_time` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '全量同步的截止时间',
  `datasource` varchar(20) CHARACTER SET utf8 DEFAULT NULL COMMENT '同步的数据库名称',
  `increment_time` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '增量同步的起始时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;

insert  into `kettle_export_table`(`table_name`,`all_time`,`datasource`,`increment_time`) values ('bas_ent','1999-01-01','certificate','1999-01-01');

INSERT INTO `db_version` VALUES ('v53_20210331', 'lxy', '2021-03-31 10:00', '新建kettle同步业务表，并赋初始值');





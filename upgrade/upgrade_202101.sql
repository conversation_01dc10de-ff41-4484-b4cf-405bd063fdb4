/*-----------------------------------------------------------------------------
  版本：v30_20210106
  编写人:lxy
  时间：2021年1月6日9:27:04
  说明：新增字典项:打印机型号
------------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795673625779765248', 'D45BT', 'D45BT打印机', 'device_type', '打印机型号', NULL, 30, '0', '1', '2021-1-4 15:22:55', '1', '2021-1-4 15:22:55', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795673572314972160', 'M32B', 'M32B便携打印机', 'device_type', '打印机型号', NULL, 20, '0', '1', '2021-1-4 15:22:43', '1', '2021-1-4 15:22:43', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795673511967326208', 'M22', 'M22便携打印机', 'device_type', '打印机型号', NULL, 10, '0', '1', '2021-1-4 15:22:28', '1', '2021-1-4 15:22:28', '', '0');

INSERT INTO `db_version` VALUES ('v30_20210106', 'lxy', '2021-01-06 09:27', '新增字典项:打印机型号');

/*-----------------------------------------------------------------------------
  版本：v31_20210106
  编写人:lxy
  时间：2021年1月6日9:28:04
  说明：新增字典项:打印机指令
------------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795673974108323840', 'cpcl', 'cpcl指令', 'command_type', '打印机指令', NULL, 20, '0', '1', '2021-1-4 15:24:18', '1', '2021-1-4 15:24:18', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795673924875583488', 'tsc', 'tsc指令', 'command_type', '打印机指令', NULL, 10, '0', '1', '2021-1-4 15:24:07', '1', '2021-1-4 15:24:07', '', '0');

INSERT INTO `db_version` VALUES ('v31_20210106', 'lxy', '2021-01-06 09:28', '新增字典项:打印机指令');

/*-----------------------------------------------------------------------------
  版本：v32_20210106
  编写人:lxy
  时间：2021年1月6日9:29:04
  说明：新增菜单:设备参数、合格证模板、合格证配置
------------------------------------------------------------------------------*/
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795661299269763072', '795660800411828224', '0,1,2,795660800411828224,', '合格证配置', 60, '/bas/certificateConfig/list', '', '', '1', 'bas:certificateConfig:view,bas:certificateConfig:edit,', '1', '2021-1-4 14:33:56', '1', '2021-1-6 09:55:33', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795661018683408384', '795660800411828224', '0,1,2,795660800411828224,', '合格证模板', 30, '/bas/certificateTemplate/list', '', '', '1', 'bas:certificateTemplate:view,bas:certificateTemplate:edit,', '1', '2021-1-4 14:32:50', '1', '2021-1-4 14:32:50', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795660800411828224', '2', '0,1,2,', '合格证管理', 63, '', '', '', '1', '', '1', '2021-1-4 14:31:57', '1', '2021-1-4 14:31:57', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795660442520256512', '781524338724569088', '0,1,2,781524338724569088,', '设备参数', 60, '/bas/deviceParameter/list', '', '', '1', 'bas:deviceParameter:view,bas:deviceParameter:edit,', '1', '2021-1-4 14:30:32', '1', '2021-1-4 14:30:32', '', '0');

INSERT INTO `db_version` VALUES ('v32_20210106', 'lxy', '2021-01-06 09:29', '设备参数、合格证模板、合格证配置');

/*-----------------------------------------------------------------------------
  版本：v33_20210106
  编写人:lxy
  时间：2021年1月6日9:30:04
  说明：新增表:合格证模板
------------------------------------------------------------------------------*/
CREATE TABLE `bas_certificate_template` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `code` tinyint(5) NOT NULL COMMENT '模板编号',
  `name` varchar(50) DEFAULT '' COMMENT '名称',
  `image_url` varchar(100) DEFAULT '' COMMENT '图片url',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合格证模板';

INSERT INTO `db_version` VALUES ('v33_20210106', 'lxy', '2021-01-06 09:30', '新增表:合格证模板');

/*-----------------------------------------------------------------------------
  版本：v34_20210106
  编写人:lxy
  时间：2021年1月6日9:31:04
  说明：新增表:蓝牙设备参数表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_device_parameter` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `name` varchar(50) DEFAULT '' COMMENT '名称',
  `type` varchar(50) DEFAULT '' COMMENT '类型',
  `command` varchar(50) DEFAULT '' COMMENT '指令',
  `image_url` varchar(100) DEFAULT '' COMMENT '图片',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='蓝牙设备参数表';

INSERT INTO `db_version` VALUES ('v34_20210106', 'lxy', '2021-01-06 09:31', '新增表:蓝牙设备参数表');

/*-----------------------------------------------------------------------------
  版本：v35_20210106
  编写人:lxy
  时间：2021年1月6日9:32:04
  说明：新增表:电子合格证配置相关表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_certificate_config` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `type` varchar(50) DEFAULT '' COMMENT '类型0=通用;1=指定区域',
  `area_id` varchar(50) DEFAULT '' COMMENT '区域编码',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='电子合格证配置表';

CREATE TABLE `bas_certificate_config_device` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `certificate_config_id` varchar(50) DEFAULT '' COMMENT '合格证配置id',
  `device_id` varchar(50) DEFAULT '' COMMENT '设备id',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='电子合格证配置-设备表';

CREATE TABLE `bas_certificate_config_template` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `certificate_config_id` varchar(50) DEFAULT '' COMMENT '合格证配置id',
  `device_id` varchar(50) DEFAULT '' COMMENT '设备id',
  `template_id` varchar(50) DEFAULT '' COMMENT '合格证模板id',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='电子合格证配置-模板表';


INSERT INTO `db_version` VALUES ('v35_20210106', 'lxy', '2021-01-06 09:32', '新增表:电子合格证配置相关表');

/*-----------------------------------------------------------------------------
  版本：v36_20210107
  编写人:lxy
  时间：2021年1月7日16:41:04
  说明：蓝牙设备参数表初始化数据
------------------------------------------------------------------------------*/
INSERT INTO `bas_device_parameter` (`id`, `name`, `type`, `command`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795674828739706880', 'M22便携打印机', 'M22', 'cpcl', 'https://jlsyncphgzqn.jlsenxiang.com/img/device/M22.png', '1', '2021-1-4 15:27:42', '1', '2021-1-4 15:27:42', NULL, '0');
INSERT INTO `bas_device_parameter` (`id`, `name`, `type`, `command`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795675374938750976', 'D45BT打印机', 'D45BT', 'tsc', 'https://jlsyncphgzqn.jlsenxiang.com/img/device/D45BT.png', '1', '2021-1-4 15:29:52', '1', '2021-1-4 15:29:52', NULL, '0');
INSERT INTO `bas_device_parameter` (`id`, `name`, `type`, `command`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795675445788934144', 'M32B便携打印机', 'M32B', 'tsc', 'https://jlsyncphgzqn.jlsenxiang.com/img/device/M32B.png', '1', '2021-1-4 15:30:09', '1', '2021-1-4 15:33:20', NULL, '0');

INSERT INTO `db_version` VALUES ('v36_20210107', 'lxy', '2021-01-07 16:41', '蓝牙设备参数表初始化数据');

/*-----------------------------------------------------------------------------
  版本：v37_20210107
  编写人:lxy
  时间：2021年1月7日16:42:04
  说明：合格证模板表初始化数据
------------------------------------------------------------------------------*/
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795663309725499392', 1, '长春市合格证(7*6cm)', 'https://jlsyncphgzqn.jlsenxiang.com/img/print-template/cc76.png', '1', '2021-1-4 14:41:56', '1', '2021-1-4 14:41:56', '', '0');
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795663404189614080', 4, '通用版合格证(7*6cm)', 'https://jlsyncphgzqn.jlsenxiang.com/img/print-template/76v2.png', '1', '2021-1-4 14:42:18', '1', '2021-1-4 14:42:18', '', '0');
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795663518861885440', 5, '人参合格证(7*6cm)', 'https://jlsyncphgzqn.jlsenxiang.com/img/print-template/76v3.png', '1', '2021-1-4 14:42:46', '1', '2021-1-4 14:43:13', '', '0');
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795663589993086976', 2, '白山市合格证(7*6cm)', 'https://jlsyncphgzqn.jlsenxiang.com/img/print-template/bs76.png', '1', '2021-1-4 14:43:03', '1', '2021-1-4 14:43:03', '', '0');
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('795663744901316608', 3, '白山市合格证(8*8cm)', 'https://jlsyncphgzqn.jlsenxiang.com/img/print-template/1.png', '1', '2021-1-4 14:43:40', '1', '2021-1-4 15:18:15', '', '0');

INSERT INTO `db_version` VALUES ('v37_20210107', 'lxy', '2021-01-07 16:42', '合格证模板表初始化数据');


/*-----------------------------------------------------------------------------
  版本：v38_20210107
  编写人:lxy
  时间：2021年1月7日16:43:04
  说明：电子合格证配置表初始化数据
------------------------------------------------------------------------------*/
INSERT INTO `bas_certificate_config` (`id`, `type`, `area_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778088766636032', '1', '220100000000', '1', '2021-1-7 16:31:40', '1', '2021-1-7 16:31:40', NULL, '0');
INSERT INTO `bas_certificate_config` (`id`, `type`, `area_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216558690304', '1', '220600000000', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config` (`id`, `type`, `area_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778286427406336', '1', '222400000000', '1', '2021-1-7 16:32:27', '1', '2021-1-7 16:32:27', NULL, '0');
INSERT INTO `bas_certificate_config` (`id`, `type`, `area_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778357843820544', '1', '220200000000', '1', '2021-1-7 16:32:44', '1', '2021-1-7 16:32:44', NULL, '0');
INSERT INTO `bas_certificate_config` (`id`, `type`, `area_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505336520704', '0', '', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');

INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778088808579072', '796778088766636032', '795675374938750976', '1', '2021-1-7 16:31:40', '1', '2021-1-7 16:31:40', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778088812773376', '796778088766636032', '795674828739706880', '1', '2021-1-7 16:31:40', '1', '2021-1-7 16:31:40', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216588050432', '796778216558690304', '795675445788934144', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216588050433', '796778216558690304', '795675374938750976', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216588050434', '796778216558690304', '795674828739706880', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778286465155072', '796778286427406336', '795674828739706880', '1', '2021-1-7 16:32:27', '1', '2021-1-7 16:32:27', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778357873180672', '796778357843820544', '795675374938750976', '1', '2021-1-7 16:32:44', '1', '2021-1-7 16:32:44', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778357873180673', '796778357843820544', '795674828739706880', '1', '2021-1-7 16:32:44', '1', '2021-1-7 16:32:44', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505361686528', '796778505336520704', '795675445788934144', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505361686529', '796778505336520704', '795675374938750976', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_device` (`id`, `certificate_config_id`, `device_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505365880832', '796778505336520704', '795674828739706880', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');

INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778088829550592', '796778088766636032', '795675374938750976', '795663404189614080', '1', '2021-1-7 16:31:40', '1', '2021-1-7 16:31:40', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778088833744896', '796778088766636032', '795674828739706880', '795663309725499392', '1', '2021-1-7 16:31:40', '1', '2021-1-7 16:31:40', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216604827648', '796778216558690304', '795675445788934144', '795663518861885440', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216604827649', '796778216558690304', '795675445788934144', '795663589993086976', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216604827650', '796778216558690304', '795675445788934144', '795663404189614080', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216609021952', '796778216558690304', '795675374938750976', '795663744901316608', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216609021953', '796778216558690304', '795675374938750976', '795663518861885440', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216609021954', '796778216558690304', '795675374938750976', '795663404189614080', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216609021955', '796778216558690304', '795674828739706880', '795663518861885440', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778216609021956', '796778216558690304', '795674828739706880', '795663404189614080', '1', '2021-1-7 16:32:10', '1', '2021-1-7 16:32:10', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778286477737984', '796778286427406336', '795674828739706880', '795663404189614080', '1', '2021-1-7 16:32:27', '1', '2021-1-7 16:32:27', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778357885763584', '796778357843820544', '795675374938750976', '795663404189614080', '1', '2021-1-7 16:32:44', '1', '2021-1-7 16:32:44', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778357889957888', '796778357843820544', '795674828739706880', '795663404189614080', '1', '2021-1-7 16:32:44', '1', '2021-1-7 16:32:44', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505378463744', '796778505336520704', '795675445788934144', '795663518861885440', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505378463745', '796778505336520704', '795675445788934144', '795663589993086976', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505382658048', '796778505336520704', '795675445788934144', '795663404189614080', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505382658049', '796778505336520704', '795675374938750976', '795663744901316608', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505382658050', '796778505336520704', '795675374938750976', '795663518861885440', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505382658051', '796778505336520704', '795675374938750976', '795663404189614080', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505382658052', '796778505336520704', '795674828739706880', '795663518861885440', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505386852352', '796778505336520704', '795674828739706880', '795663404189614080', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');
INSERT INTO `bas_certificate_config_template` (`id`, `certificate_config_id`, `device_id`, `template_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('796778505386852353', '796778505336520704', '795674828739706880', '795663309725499392', '1', '2021-1-7 16:33:19', '1', '2021-1-7 16:33:19', NULL, '0');

INSERT INTO `db_version` VALUES ('v38_20210107', 'lxy', '2021-01-07 16:43', '电子合格证配置表初始化数据');

/*-----------------------------------------------------------------------------
  版本：v39_20210113
  编写人:lxy
  时间：2021年1月13日11:00:00
  说明：主体信息表增加字段：开具次数、开具数量
------------------------------------------------------------------------------*/
ALTER TABLE `bas_ent`
ADD COLUMN `certificate_amount`  int NULL DEFAULT 0 COMMENT '开具次数' AFTER `basic_enter_flag`,
ADD COLUMN `certificate_print_amount`  int NULL DEFAULT 0 COMMENT '开具数量' AFTER `certificate_amount`;

INSERT INTO `db_version` VALUES ('v39_20210113', 'lxy', '2021-01-13 11:00', '主体信息表增加字段：开具次数、开具数量');

/*-----------------------------------------------------------------------------
  版本：v40_20210113
  编写人:lxy
  时间：2021年1月13日11:10:00
  说明：主体信息表更新开具次数、开具数量数据
------------------------------------------------------------------------------*/
UPDATE bas_ent t
INNER JOIN (
    SELECT
        c.ent_id AS entId,
        COUNT(c.id) AS certificateAmount,
        SUM(IFNULL(c.print_count, 0)) AS certificatePrintAmount
    FROM
        bas_certificate c
    GROUP BY
        c.ent_id
) a ON t.id = a.entId
SET t.certificate_amount = a.certificateAmount,
 t.certificate_print_amount = a.certificatePrintAmount
;

INSERT INTO `db_version` VALUES ('v40_20210113', 'lxy', '2021-01-13 11:10', '主体信息表更新开具次数、开具数量数据');


/*-----------------------------------------------------------------------------
  版本：v41_20210115
  编写人:lxy
  时间：2021年1月15日9:27:04
  说明：检测情况:增加新字典项、调整排序
------------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('799636130860367872', 'is04', '自检合格', 'inspection_situation_code', '检测情况', NULL, 30, '0', '1', '2021-1-15 13:48:30', '1', '2021-1-15 13:48:30', '', '0');
UPDATE `sys_dict` SET `label`='快检合格', `sort`='20' WHERE (`id`='753660953416957952');
UPDATE `sys_dict` SET `sort`='40' WHERE (`id`='756177591244685312');

INSERT INTO `db_version` VALUES ('v41_20210115', 'lxy', '2021-01-15 09:27', '检测情况:增加新字典项、调整排序');

/*-----------------------------------------------------------------------------
  版本：v42_20210118
  编写人:lxy
  时间：2021年1月18日10:30:00
  说明：新增：产品检测表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_product_inspection` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `ent_id` varchar(50) DEFAULT '' COMMENT '主体id',
  `product_id` varchar(50) DEFAULT '' COMMENT '产品id',
  `product_name` varchar(50) DEFAULT '' COMMENT '产品名称',
  `sample_no` varchar(50) DEFAULT '' COMMENT '样品编号',
  `sample_qrcode_url` varchar(100) DEFAULT '' COMMENT '二维码url',
  `record_no` varchar(50) DEFAULT '' COMMENT '检测编号',
  `inspection_situation` varchar(10) DEFAULT '' COMMENT '检测情况',
  `inspection_item` varchar(50) DEFAULT '' COMMENT '检测项目',
  `inspection_date` datetime DEFAULT NULL COMMENT '检测时间',
  `inspection_standard` varchar(50) DEFAULT '' COMMENT '检测标准',
  `inspection_result` varchar(1) DEFAULT '' COMMENT '检测结果',
  `inspection_person` varchar(50) DEFAULT '' COMMENT '检测人员',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产品检测表';

INSERT INTO `db_version` VALUES ('v42_20210118', 'lxy', '2021-01-18 10:30', '新增表：产品检测表');

/*-----------------------------------------------------------------------------
  版本：v43_20210118
  编写人:lxy
  时间：2021年1月18日11:30:00
  说明：新增：产品样品表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_product_sample` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `ent_id` varchar(50) DEFAULT '' COMMENT '主体id',
  `product_id` varchar(50) DEFAULT '' COMMENT '产品id',
  `inspection_situation` varchar(50) DEFAULT '' COMMENT '检测情况',
  `product_inspection_id` varchar(50) DEFAULT '' COMMENT '检测结果表id',
  `sample_no` varchar(50) DEFAULT '' COMMENT '样品编号',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产品样品表';

INSERT INTO `db_version` VALUES ('v43_20210118', 'lxy', '2021-01-18 11:30', '新增：产品样品表');

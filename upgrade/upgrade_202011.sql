/*-----------------------------------------------------------------------------
  版本：v22_20201105
  编写人:lxy
  时间：2020年11月5日18:30:15
  说明：行政区划增加长春莲花山生态旅游度假区
------------------------------------------------------------------------------*/
insert into `sys_area` (`id`, `parent_id`, `parent_ids`, `name`, `code`, `type`, `lng`, `lat`, `sort`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) values('220190000000','220100000000','0,000000000000,220000000000,220100000000,','长春莲花山生态旅游度假区','220190','4',NULL,NULL,'180','1','2020-11-05 18:19:47','1','2020-11-05 18:19:47','','0');

INSERT INTO `db_version` VALUES ('v22_20201105', 'lxy', '2020-11-05 18:30', '行政区划增加长春莲花山生态旅游度假区');

/*-----------------------------------------------------------------------------
  版本：v23_20201112
  编写人:lxy
  时间：2020年11月12日10:18:15
  说明：行政区划-县区级"梅河口市、公主岭市"改成"梅河口，公主岭"
------------------------------------------------------------------------------*/
UPDATE `sys_area` SET `name`='梅河口' WHERE (`id`='222610000000');
UPDATE `sys_area` SET `name`='公主岭' WHERE (`id`='222710000000');

INSERT INTO `db_version` VALUES ('v23_20201112', 'lxy', '2020-11-12 10:18', '行政区划-县区级"梅河口市、公主岭市"改成"梅河口，公主岭"');

/*-----------------------------------------------------------------------------
  版本：v24_20201127
  编写人:lxy
  时间：2020年11月27日9:30:03
  说明：添加系统菜单
------------------------------------------------------------------------------*/
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('781524562826231808', '781524338724569088', '0,1,2,781524338724569088,', '蓝牙设备', 30, '/bas/bluetoothDevice/list', '', '', '1', 'bas:bluetoothDevice:view,bas:bluetoothDevice:edit,', '1', '2020-11-26 14:19:36', '1', '2020-11-26 14:20:28', '', '0');
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('781524338724569088', '2', '0,1,2,', '设备管理', 33, '', '', '', '1', '', '1', '2020-11-26 14:18:42', '1', '2020-11-26 14:18:42', '', '0');

INSERT INTO `db_version` VALUES ('v24_20201127', 'lxy', '2020-11-27 09:30', '添加系统菜单');

/*-----------------------------------------------------------------------------
  版本：v25_20201127
  编写人:lxy
  时间：2020年11月27日9:31:03
  说明：创建蓝牙设备表
------------------------------------------------------------------------------*/
CREATE TABLE `bas_bluetooth_device` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `brand_name` varchar(50) DEFAULT '' COMMENT '品牌名称',
  `device_name` varchar(50) DEFAULT '' COMMENT '设备名称',
  `mac_code` varchar(50) DEFAULT '' COMMENT '设备mac编码',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(225) DEFAULT NULL COMMENT '备注',
  `del_flag` varchar(1) DEFAULT NULL COMMENT '删除标示',
  PRIMARY KEY (`id`),
  KEY `index_device_name` (`device_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='蓝牙设备表';

INSERT INTO `db_version` VALUES ('v25_20201127', 'lxy', '2020-11-27 09:31', '创建蓝牙设备表');
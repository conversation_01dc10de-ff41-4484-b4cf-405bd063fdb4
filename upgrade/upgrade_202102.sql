/*-----------------------------------------------------------------------------
  版本：v44_20210222
  编写人:lxy
  时间：2021年2月22日16:12:00
  说明：修改合格证表：添加产品检测表id
------------------------------------------------------------------------------*/
ALTER TABLE `bas_certificate`
ADD COLUMN `product_inspection_id`  varchar(50) NULL DEFAULT '' COMMENT '产品检测表id' AFTER `sample_no`;

INSERT INTO `db_version` VALUES ('v44_20210222', 'lxy', '2021-02-22 16:12', '修改合格证表：添加产品检测表id');

/*-----------------------------------------------------------------------------
  版本：v45_20210225
  编写人:lxy
  时间：2021年2月25日10:07:00
  说明：基础信息表：添加业务表名
------------------------------------------------------------------------------*/
ALTER TABLE `bas_basic_ent`
ADD COLUMN `table_name`  varchar(50) NULL DEFAULT '' COMMENT '业务表名' AFTER `id`;

INSERT INTO `db_version` VALUES ('v45_20210225', 'lxy', '2021-02-25 10:07', '基础信息表：添加业务表名');

/*-----------------------------------------------------------------------------
  版本：v46_20210225
  编写人:lxy
  时间：2021年2月25日10:16:00
  说明：基础信息表：初始化table_name数据
------------------------------------------------------------------------------*/
update bas_basic_ent t set t.table_name='bas_ent' where t.table_name ="";

INSERT INTO `db_version` VALUES ('v46_20210225', 'lxy', '2021-02-25 10:16', '基础信息表：初始化table_name数据');


/*-----------------------------------------------------------------------------
  版本：v47_20210226
  编写人:lxy
  时间：2021年2月26日11:19:00
  说明：添加菜单:变更审核
------------------------------------------------------------------------------*/
INSERT INTO `sys_menu` (`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('814500298176331776', '735152670842552320', '0,1,722774558318264320,735152670842552320,', '变更审核', 90, '/bas/entChange/examine/list', '', '', '1', 'bas:entChange:view,bas:entChange:edit', '1', '2021-2-25 14:13:24', '1', '2021-2-25 14:13:24', '', '0');

INSERT INTO `db_version` VALUES ('v47_20210226', 'lxy', '2021-02-26 11:19', '添加菜单:变更审核');

/*-----------------------------------------------------------------------------
  版本：v48_20210226
  编写人:lxy
  时间：2021年2月26日11:20:00
  说明：监管人角色添加"变更审核"菜单
------------------------------------------------------------------------------*/
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES ('734042082313764864', '814500298176331776');

INSERT INTO `db_version` VALUES ('v48_20210226', 'lxy', '2021-02-26 11:20', '监管人角色添加"变更审核"菜单');
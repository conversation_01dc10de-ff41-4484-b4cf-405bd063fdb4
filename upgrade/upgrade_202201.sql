/*-----------------------------------------------------------------------------
  版本：v102_20220104
  编写人:lxy
  时间：2022年1月4日09:00:00
  说明：产品增加业务字段
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_product`
ADD COLUMN `electricity_link`  varchar(50) NULL DEFAULT '' COMMENT '电商链接' AFTER `query_code_url`,
ADD COLUMN `scale_amount`  varchar(50) NULL DEFAULT '' COMMENT '种养殖规模' AFTER `electricity_link`,
ADD COLUMN `scale_unit_code`  varchar(50) NULL DEFAULT '' COMMENT '规模单位code' AFTER `scale_amount`,
ADD COLUMN `scale_unit_name`  varchar(50) NULL DEFAULT '' COMMENT '规模单位name' AFTER `scale_unit_code`,
ADD COLUMN `production_value`  varchar(50) NULL DEFAULT '' COMMENT '年产值' AFTER `scale_unit_name`,
ADD COLUMN `production_amount`  varchar(50) NULL DEFAULT '' COMMENT '年产量' AFTER `production_value`,
ADD COLUMN `production_unit_code`  varchar(50) NULL DEFAULT '' COMMENT '年产量单位code' AFTER `production_amount`,
ADD COLUMN `production_unit_name`  varchar(50) NULL DEFAULT '' COMMENT '年产量单位name' AFTER `production_unit_code`;

INSERT INTO `db_version` VALUES ('v102_20220104', 'lxy', '2022-01-04 09:00', '产品增加业务字段');

/*-----------------------------------------------------------------------------
  版本：v103_20220104
  编写人:lxy
  时间：2022年1月4日10:00:00
  说明：产品单位字典项增加
 -----------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('927858971102937088', '80', '亩', 'product_unit_code', '产品单位', NULL, 80, '0', '1', '2022-1-4 09:40:17', '1', '2022-1-4 09:40:17', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('927859010160295936', '90', '丈', 'product_unit_code', '产品单位', NULL, 90, '0', '1', '2022-1-4 09:40:27', '1', '2022-1-4 09:40:27', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('927859055790129152', '100', '箱', 'product_unit_code', '产品单位', NULL, 100, '0', '1', '2022-1-4 09:40:38', '1', '2022-1-4 09:40:38', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('927859104209174528', '110', '袋', 'product_unit_code', '产品单位', NULL, 110, '0', '1', '2022-1-4 09:40:49', '1', '2022-1-4 09:40:49', '', '0');
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('927859153517412352', '120', '尾', 'product_unit_code', '产品单位', NULL, 120, '0', '1', '2022-1-4 09:41:01', '1', '2022-1-4 09:41:01', '', '0');

INSERT INTO `db_version` VALUES ('v103_20220104', 'lxy', '2022-01-04 10:00', '产品单位字典项增加');

/*-----------------------------------------------------------------------------
  版本：v104_20220104
  编写人:lxy
  时间：2022年1月4日11:00:00
  说明：检测情况字典项增加
 -----------------------------------------------------------------------------*/
INSERT INTO `sys_dict` (`id`, `value`, `label`, `type`, `description`, `category`, `sort`, `parent_id`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('927879071612796928', 'is05', '内部质量控制', 'inspection_situation_code', '检测情况', NULL, 50, '0', '1', '2022-1-4 11:00:10', '1', '2022-1-4 11:00:10', '', '0');

INSERT INTO `db_version` VALUES ('v104_20220104', 'lxy', '2022-01-04 11:00', '检测情况字典项增加');

/*-----------------------------------------------------------------------------
  版本：v105_20220104
  编写人:lxy
  时间：2022年1月4日12:00:00
  说明：产品检测表增加字段
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_product_inspection`
ADD COLUMN `inspection_explain`  varchar(200) NULL DEFAULT '' COMMENT '检测说明' AFTER `inspection_person`;

INSERT INTO `db_version` VALUES ('v105_20220104', 'lxy', '2022-01-04 12:00', '产品检测表增加字段');

/*-----------------------------------------------------------------------------
  版本：v106_20220104
  编写人:lxy
  时间：2022年1月4日13:00:00
  说明：调整检测情况字典项
 -----------------------------------------------------------------------------*/
UPDATE `sys_dict` SET `label`='自我承诺' WHERE (`id`='753660768775307264');
UPDATE `sys_dict` SET `del_flag`='1' WHERE (`id`='753660953416957952');

INSERT INTO `db_version` VALUES ('v106_20220104', 'lxy', '2022-01-04 13:00', '调整检测情况字典项');

/*-----------------------------------------------------------------------------
  版本：v107_20220105
  编写人:lxy
  时间：2022年1月5日09:00:00
  说明：合格证模板添加
 -----------------------------------------------------------------------------*/
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `show_village`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('928288105599860736', 6, '承诺达标合格证(7*7cm)', 'https://jlsyncphgzqn.jikeruan.com/img/print-template/77v1.png', '1', '1', '2022-1-5 14:05:31', '1', '2022-1-5 14:05:31', NULL, '0');
INSERT INTO `bas_certificate_template` (`id`, `code`, `name`, `image_url`, `show_village`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES ('928288313385680896', 7, '人参初级产品承诺达标合格证(7*7cm)', 'https://jlsyncphgzqn.jikeruan.com/img/print-template/77v2.png', '1', '1', '2022-1-5 14:06:21', '1', '2022-1-5 14:06:21', NULL, '0');

INSERT INTO `db_version` VALUES ('v107_20220105', 'lxy', '2022-01-05 09:00', '合格证模板添加');

/*-----------------------------------------------------------------------------
  版本：v108_20220110
  编写人:lxy
  时间：2022年1月10日14:11:17
  说明：产品添加业务字段
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_product`
ADD COLUMN `print_amount`  int NULL DEFAULT 0 COMMENT '开具数量' AFTER `production_unit_name`,
ADD COLUMN `print_date`  datetime NULL DEFAULT NULL COMMENT '开具时间' AFTER `print_amount`;

INSERT INTO `db_version` VALUES ('v108_20220110', 'lxy', '2022-01-10 14:11', '产品添加业务字段');

/*-----------------------------------------------------------------------------
  版本：v109_20220110
  编写人:lxy
  时间：2022年1月10日15:13:17
  说明：更新产品打印数量
 -----------------------------------------------------------------------------*/
update bas_product t 
LEFT JOIN
(select product_id,SUM(print_count) printCount,MAX(create_date) createDate from bas_certificate GROUP BY product_id) a on t.id=a.product_id
set t.print_amount=IFNULL(a.printCount,0),t.print_date=IFNULL(a.createDate,null);

INSERT INTO `db_version` VALUES ('v109_20220110', 'lxy', '2022-01-10 15:13', '更新产品打印数量');

/*-----------------------------------------------------------------------------
  版本：v110_20220110
  编写人:lxy
  时间：2022年1月10日15:38:17
  说明：主体增加属性“开具时间”
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_ent`
ADD COLUMN `certificate_print_date`  datetime NULL DEFAULT NULL COMMENT '开具时间' AFTER `certificate_print_amount`;

INSERT INTO `db_version` VALUES ('v110_20220110', 'lxy', '2022-01-10 15:38', '主体增加属性“开具时间”');


/*-----------------------------------------------------------------------------
  版本：v111_20220110
  编写人:lxy
  时间：2022年1月10日15:42:17
  说明：更新主体开具时间
 -----------------------------------------------------------------------------*/
update bas_ent t 
LEFT JOIN
(select ent_id,MAX(create_date) createDate from bas_certificate GROUP BY ent_id) a on t.id=a.ent_id
set t.certificate_print_date=IFNULL(a.createDate,null);

INSERT INTO `db_version` VALUES ('v111_20220110', 'lxy', '2022-01-10 15:42', '更新主体开具时间');

/*-----------------------------------------------------------------------------
  版本：v112_20220111
  编写人:lxy
  时间：2022年1月11日12:00:00
  说明：扫描日志表增加字段
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_scan_record`
ADD COLUMN `province`  varchar(50) NULL DEFAULT '' COMMENT '省' AFTER `scan_date`,
ADD COLUMN `city`  varchar(50) NULL DEFAULT '' COMMENT '市' AFTER `province`,
ADD COLUMN `county`  varchar(50) NULL DEFAULT '' COMMENT '区' AFTER `city`,
ADD COLUMN `ip`  varchar(50) NULL DEFAULT '' COMMENT 'ip' AFTER `county`;

INSERT INTO `db_version` VALUES ('v112_20220111', 'lxy', '2022-01-11 12:00', '扫描日志表增加字段');

/*-----------------------------------------------------------------------------
  版本：v113_20220112
  编写人:lxy
  时间：2022年1月12日14:51:00
  说明：产品检测表增加业务字段
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_product_inspection`
ADD COLUMN `inspection_company`  varchar(50) NULL DEFAULT '' COMMENT '检测单位' AFTER `inspection_explain`,
ADD COLUMN `inspection_value`  varchar(50) NULL DEFAULT '' COMMENT '检测数值' AFTER `inspection_company`,
ADD COLUMN `acceptable_range`  varchar(50) NULL DEFAULT '' COMMENT '合格范围' AFTER `inspection_value`;

INSERT INTO `db_version` VALUES ('v113_20220112', 'lxy', '2022-01-12 14:51', '产品检测表增加业务字段');

/*-----------------------------------------------------------------------------
  版本：v114_20220113
  编写人:lxy
  时间：2022年1月13日10:48:00
  说明：行政区划更改梅河口地区名称
 -----------------------------------------------------------------------------*/
UPDATE `sys_area` SET `name`='梅河口' WHERE (`id`='222610000000');

INSERT INTO `db_version` VALUES ('v114_20220113', 'lxy', '2022-01-13 10:48', '行政区划更改梅河口地区名称');


/*-----------------------------------------------------------------------------
  版本：v115_20220113
  编写人:lxy
  时间：2022年1月13日16:36:00
  说明：反馈信息表更改字段数据库编码格式
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_feedback`
MODIFY COLUMN `content`  varchar(500) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT '反馈内容' AFTER `phone`;

INSERT INTO `db_version` VALUES ('v115_20220113', 'lxy', '2022-01-13 16:36', '反馈信息表更改字段数据库编码格式');

/*-----------------------------------------------------------------------------
  版本：v116_20220119
  编写人:lxy
  时间：2022年1月19日16:34:00
  说明：产品表修改电商链接长度
 -----------------------------------------------------------------------------*/
ALTER TABLE `bas_product`
MODIFY COLUMN `electricity_link`  varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '电商链接' AFTER `query_code_url`;

INSERT INTO `db_version` VALUES ('v116_20220119', 'lxy', '2022-01-19 16:34', '产品表修改电商链接长度');


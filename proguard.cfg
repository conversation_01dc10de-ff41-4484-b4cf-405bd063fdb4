# ----------------------------------
#  通过指定数量的优化能执行
#  -optimizationpasses n
# ----------------------------------
-optimizationpasses 3

# ----------------------------------
#   混淆时不会产生形形色色的类名 
#   -dontusemixedcaseclassnames
# ----------------------------------
-dontusemixedcaseclassnames

# ----------------------------------
#      指定不去忽略非公共的库类
#  -dontskipnonpubliclibraryclasses
# ----------------------------------
#-dontskipnonpubliclibraryclasses

# ----------------------------------
#       不预校验
#    -dontpreverify
# ----------------------------------
#-dontpreverify

# ----------------------------------
#      输出生成信息
#       -verbose
# ----------------------------------
-verbose

#混淆时应用侵入式重载 
-overloadaggressively 
 
#优化时允许访问并修改有修饰符的类和类的成员 
-allowaccessmodification

#确定统一的混淆类的成员名称来增加混淆 
-useuniqueclassmembernames

-dontwarn

#这里添加你不需要混淆的类
-keep public class org.** {*;}
-keep public class sun.** {*;}
-keep public class com.thinkgem.jeesite.test.** {*;}
-keep public class com.thinkgem.jeesite.**.dao.** {*;}
-keep public class com.thinkgem.jeesite.**.entity.** {*;}
-keep public class com.thinkgem.jeesite.modules.act.utils.Variable {*;}
-keep public class com.thinkgem.jeesite.common.persistence.Page {*;}

-keep public class * extends javax.servlet.Servlet

-keepattributes **

#-keepnames class * implements java.io.Serializable
# ---------保护所有实体中的字段名称----------
-keepclassmembers class * implements java.io.Serializable {
    <fields>;
}
-keepclassmembers class * implements com.thinkgem.jeesite.common.persistence.BaseEntity {
    <fields>;
}

# --------- 保护类中的所有方法名 ------------
-keepclassmembers class * {
	public <methods>;
}